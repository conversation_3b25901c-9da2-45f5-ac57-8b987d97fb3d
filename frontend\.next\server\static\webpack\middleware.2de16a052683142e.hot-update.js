"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./middleware.ts":
/*!***********************!*\
  !*** ./middleware.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   middleware: () => (/* binding */ middleware)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n/**\n * Next.js middleware for authentication and security\n */ \n// Routes that require authentication\nconst protectedRoutes = [\n    \"/dashboard\",\n    \"/profile\",\n    \"/resumes\",\n    \"/cover-letters\",\n    \"/linkedin-bios\",\n    \"/settings\"\n];\n// Routes that should redirect authenticated users\nconst authRoutes = [\n    \"/auth/login\",\n    \"/auth/register\",\n    \"/login\",\n    \"/register\"\n];\n// Public routes that don't require authentication\nconst publicRoutes = [\n    \"/\",\n    \"/about\",\n    \"/pricing\",\n    \"/contact\",\n    \"/privacy\",\n    \"/terms\"\n];\n/**\n * Check if user is authenticated by verifying the httpOnly cookie\n */ async function isAuthenticated(request) {\n    const token = request.cookies.get(\"token\");\n    if (!token) {\n        return false;\n    }\n    try {\n        // Verify token with backend\n        const response = await fetch(`${\"http://localhost:5000\"}/api/auth/me`, {\n            method: \"GET\",\n            headers: {\n                \"Authorization\": `Bearer ${token.value}`,\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        return response.ok;\n    } catch (error) {\n        console.error(\"Token verification error:\", error);\n        return false;\n    }\n}\n/**\n * Security headers for all responses\n */ function addSecurityHeaders(response) {\n    // Content Security Policy\n    response.headers.set(\"Content-Security-Policy\", \"default-src 'self'; \" + \"script-src 'self' 'unsafe-inline' 'unsafe-eval' https://checkout.razorpay.com; \" + \"style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; \" + \"font-src 'self' https://fonts.gstatic.com; \" + \"img-src 'self' data: https:; \" + \"connect-src 'self' \" + \"http://localhost:5000\" + \" https://api.razorpay.com; \" + \"frame-src https://api.razorpay.com; \" + \"object-src 'none'; \" + \"base-uri 'self';\");\n    // Security headers\n    response.headers.set(\"X-Frame-Options\", \"DENY\");\n    response.headers.set(\"X-Content-Type-Options\", \"nosniff\");\n    response.headers.set(\"Referrer-Policy\", \"strict-origin-when-cross-origin\");\n    response.headers.set(\"X-XSS-Protection\", \"1; mode=block\");\n    // HSTS (only in production)\n    if (false) {}\n    // Permissions Policy\n    response.headers.set(\"Permissions-Policy\", \"camera=(), microphone=(), geolocation=(), payment=(self)\");\n    return response;\n}\n/**\n * Rate limiting (simple in-memory implementation)\n */ const rateLimitMap = new Map();\nfunction isRateLimited(ip, limit = 100, windowMs = 15 * 60 * 1000) {\n    const now = Date.now();\n    const record = rateLimitMap.get(ip);\n    if (!record || now > record.resetTime) {\n        rateLimitMap.set(ip, {\n            count: 1,\n            resetTime: now + windowMs\n        });\n        return false;\n    }\n    if (record.count >= limit) {\n        return true;\n    }\n    record.count++;\n    return false;\n}\nasync function middleware(request) {\n    const { pathname } = request.nextUrl;\n    // Get client IP for rate limiting\n    const ip = request.ip || request.headers.get(\"x-forwarded-for\") || \"unknown\";\n    // Apply rate limiting\n    if (isRateLimited(ip)) {\n        return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(\"Too Many Requests\", {\n            status: 429\n        });\n    }\n    // Check authentication status\n    const authenticated = await isAuthenticated(request);\n    // Handle protected routes\n    if (protectedRoutes.some((route)=>pathname.startsWith(route))) {\n        if (!authenticated) {\n            const loginUrl = new URL(\"/auth/login\", request.url);\n            loginUrl.searchParams.set(\"redirect\", pathname);\n            const response = next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(loginUrl);\n            return addSecurityHeaders(response);\n        }\n    }\n    // Handle auth routes (redirect if already authenticated)\n    if (authRoutes.some((route)=>pathname.startsWith(route))) {\n        if (authenticated) {\n            const dashboardUrl = new URL(\"/dashboard\", request.url);\n            const response = next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(dashboardUrl);\n            return addSecurityHeaders(response);\n        }\n    }\n    // Handle API routes\n    if (pathname.startsWith(\"/api/\")) {\n        // Add CORS headers for API routes\n        const response = next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n        // CORS configuration\n        const origin = request.headers.get(\"origin\");\n        const allowedOrigins = [\n            \"http://localhost:3000\" || 0,\n            \"http://localhost:3000\",\n            \"https://localhost:3000\"\n        ];\n        if (origin && allowedOrigins.includes(origin)) {\n            response.headers.set(\"Access-Control-Allow-Origin\", origin);\n        }\n        response.headers.set(\"Access-Control-Allow-Credentials\", \"true\");\n        response.headers.set(\"Access-Control-Allow-Methods\", \"GET, POST, PUT, DELETE, OPTIONS\");\n        response.headers.set(\"Access-Control-Allow-Headers\", \"Content-Type, Authorization, X-CSRF-Token\");\n        // Handle preflight requests\n        if (request.method === \"OPTIONS\") {\n            return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(null, {\n                status: 200,\n                headers: response.headers\n            });\n        }\n        return addSecurityHeaders(response);\n    }\n    // For all other routes, just add security headers\n    const response = next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n    return addSecurityHeaders(response);\n}\nconst config = {\n    matcher: [\n        /*\n     * Match all request paths except for the ones starting with:\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * - public folder files\n     */ \"/((?!_next/static|_next/image|favicon.ico|public/).*)\"\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./middleware.ts\n");

/***/ })

});