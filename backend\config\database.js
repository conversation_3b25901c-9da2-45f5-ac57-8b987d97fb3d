/**
 * Enhanced database configuration with connection pooling and optimization
 */
const mongoose = require("mongoose");
const logger = require("../utils/logger");

/**
 * Database connection configuration
 */
const connectDB = async () => {
  try {
    // Connection options for production optimization
    const options = {
      // Connection pool settings
      maxPoolSize: 10, // Maximum number of connections in the pool
      minPoolSize: 2, // Minimum number of connections in the pool
      maxIdleTimeMS: 30000, // Close connections after 30 seconds of inactivity
      serverSelectionTimeoutMS: 5000, // How long to try selecting a server
      socketTimeoutMS: 45000, // How long a send or receive on a socket can take

      // Buffering settings (removed bufferMaxEntries as it's deprecated)
      bufferCommands: false, // Disable mongoose buffering

      // Heartbeat settings
      heartbeatFrequencyMS: 10000, // How often to check server status

      // Retry settings
      retryWrites: true,
      retryReads: true,

      // Write concern
      w: "majority",

      // Read preference
      readPreference: "primary",

      // Compression
      compressors: ["zlib"],

      // Authentication
      authSource: "admin",

      // SSL/TLS (enable in production)
      ...(process.env.NODE_ENV === "production" && {
        ssl: true,
        sslValidate: true,
      }),
    };

    // Connect to MongoDB
    const conn = await mongoose.connect(process.env.MONGO_URI, options);

    logger.info("Database connected successfully", {
      host: conn.connection.host,
      port: conn.connection.port,
      database: conn.connection.name,
      readyState: conn.connection.readyState,
    });

    // Connection event handlers
    mongoose.connection.on("connected", () => {
      logger.info("Mongoose connected to MongoDB");
    });

    mongoose.connection.on("error", (err) => {
      logger.error("Mongoose connection error", { error: err.message });
    });

    mongoose.connection.on("disconnected", () => {
      logger.warn("Mongoose disconnected from MongoDB");
    });

    // Handle application termination
    process.on("SIGINT", async () => {
      try {
        await mongoose.connection.close();
        logger.info("Mongoose connection closed through app termination");
        process.exit(0);
      } catch (error) {
        logger.error("Error closing mongoose connection", {
          error: error.message,
        });
        process.exit(1);
      }
    });

    return conn;
  } catch (error) {
    logger.error("Database connection failed", {
      error: error.message,
      stack: error.stack,
    });

    // Exit process with failure
    process.exit(1);
  }
};

/**
 * Database health check
 */
const checkDatabaseHealth = async () => {
  try {
    const state = mongoose.connection.readyState;
    const states = {
      0: "disconnected",
      1: "connected",
      2: "connecting",
      3: "disconnecting",
    };

    if (state === 1) {
      // Perform a simple query to test connectivity
      await mongoose.connection.db.admin().ping();
      return {
        status: "healthy",
        state: states[state],
        host: mongoose.connection.host,
        database: mongoose.connection.name,
      };
    } else {
      return {
        status: "unhealthy",
        state: states[state],
        message: "Database not connected",
      };
    }
  } catch (error) {
    logger.error("Database health check failed", { error: error.message });
    return {
      status: "unhealthy",
      error: error.message,
    };
  }
};

/**
 * Get database statistics
 */
const getDatabaseStats = async () => {
  try {
    if (mongoose.connection.readyState !== 1) {
      throw new Error("Database not connected");
    }

    const db = mongoose.connection.db;
    const stats = await db.stats();

    return {
      collections: stats.collections,
      dataSize: stats.dataSize,
      storageSize: stats.storageSize,
      indexes: stats.indexes,
      indexSize: stats.indexSize,
      objects: stats.objects,
      avgObjSize: stats.avgObjSize,
    };
  } catch (error) {
    logger.error("Failed to get database stats", { error: error.message });
    throw error;
  }
};

/**
 * Create database indexes for all models
 */
const createIndexes = async () => {
  try {
    logger.info("Creating database indexes...");

    // Get all models
    const models = mongoose.modelNames();

    for (const modelName of models) {
      const model = mongoose.model(modelName);
      await model.createIndexes();
      logger.debug(`Indexes created for ${modelName} model`);
    }

    logger.info("All database indexes created successfully");
  } catch (error) {
    logger.error("Failed to create database indexes", { error: error.message });
    throw error;
  }
};

/**
 * Database cleanup and optimization
 */
const optimizeDatabase = async () => {
  try {
    if (mongoose.connection.readyState !== 1) {
      throw new Error("Database not connected");
    }

    const db = mongoose.connection.db;

    // Get collection names
    const collections = await db.listCollections().toArray();

    for (const collection of collections) {
      const collectionName = collection.name;

      // Compact collection (MongoDB 4.4+)
      try {
        await db.command({ compact: collectionName });
        logger.debug(`Compacted collection: ${collectionName}`);
      } catch (error) {
        // Compact might not be available in all MongoDB versions
        logger.debug(
          `Could not compact collection ${collectionName}: ${error.message}`
        );
      }
    }

    logger.info("Database optimization completed");
  } catch (error) {
    logger.error("Database optimization failed", { error: error.message });
    throw error;
  }
};

/**
 * Graceful shutdown
 */
const gracefulShutdown = async () => {
  try {
    logger.info("Initiating graceful database shutdown...");

    // Close all connections
    await mongoose.connection.close();

    logger.info("Database connections closed successfully");
  } catch (error) {
    logger.error("Error during graceful shutdown", { error: error.message });
    throw error;
  }
};

module.exports = {
  connectDB,
  checkDatabaseHealth,
  getDatabaseStats,
  createIndexes,
  optimizeDatabase,
  gracefulShutdown,
};
