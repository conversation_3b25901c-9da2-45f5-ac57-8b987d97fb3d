globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/(dashboard)/profile/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./app/(auth)/login/page.tsx":{"*":{"id":"(ssr)/./app/(auth)/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/theme-provider.tsx":{"*":{"id":"(ssr)/./components/theme-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/ui/toaster.tsx":{"*":{"id":"(ssr)/./components/ui/toaster.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./lib/auth-context.tsx":{"*":{"id":"(ssr)/./lib/auth-context.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/not-found.tsx":{"*":{"id":"(ssr)/./app/not-found.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/(auth)/register/page.tsx":{"*":{"id":"(ssr)/./app/(auth)/register/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/landing-header.tsx":{"*":{"id":"(ssr)/./components/landing-header.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/link.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/(dashboard)/dashboard/page.tsx":{"*":{"id":"(ssr)/./app/(dashboard)/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/(dashboard)/resumes/page.tsx":{"*":{"id":"(ssr)/./app/(dashboard)/resumes/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/(dashboard)/profile/page.tsx":{"*":{"id":"(ssr)/./app/(dashboard)/profile/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\app\\(auth)\\login\\page.tsx":{"id":"(app-pages-browser)/./app/(auth)/login/page.tsx","name":"*","chunks":[],"async":false},"D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\app\\globals.css":{"id":"(app-pages-browser)/./app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\components\\theme-provider.tsx":{"id":"(app-pages-browser)/./components/theme-provider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\components\\ui\\toaster.tsx":{"id":"(app-pages-browser)/./components/ui/toaster.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\lib\\auth-context.tsx":{"id":"(app-pages-browser)/./lib/auth-context.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\app\\not-found.tsx":{"id":"(app-pages-browser)/./app/not-found.tsx","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\node_modules\\next\\dist\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\node_modules\\next\\dist\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\app\\(auth)\\register\\page.tsx":{"id":"(app-pages-browser)/./app/(auth)/register/page.tsx","name":"*","chunks":[],"async":false},"D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\components\\landing-header.tsx":{"id":"(app-pages-browser)/./components/landing-header.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\node_modules\\next\\dist\\client\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/link.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\node_modules\\next\\dist\\esm\\client\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/link.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\app\\(dashboard)\\dashboard\\page.tsx":{"id":"(app-pages-browser)/./app/(dashboard)/dashboard/page.tsx","name":"*","chunks":[],"async":false},"D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\app\\(dashboard)\\resumes\\page.tsx":{"id":"(app-pages-browser)/./app/(dashboard)/resumes/page.tsx","name":"*","chunks":[],"async":false},"D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\app\\(dashboard)\\profile\\page.tsx":{"id":"(app-pages-browser)/./app/(dashboard)/profile/page.tsx","name":"*","chunks":["app/(dashboard)/profile/page","static/chunks/app/(dashboard)/profile/page.js"],"async":false}},"entryCSSFiles":{"D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\":[],"D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\app\\layout":["static/css/app/layout.css"],"D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\app\\not-found":[],"D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\app\\page":[],"D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\app\\(dashboard)\\profile\\page":[]}}