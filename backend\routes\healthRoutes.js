/**
 * Health check and monitoring routes
 */
const express = require("express");
const { checkDatabaseHealth, getDatabaseStats } = require("../config/database");
const logger = require("../utils/logger");
const responseFormatter = require("../utils/responseFormatter");

const router = express.Router();

/**
 * @desc    Basic health check
 * @route   GET /api/health
 * @access  Public
 */
router.get("/", async (req, res) => {
  try {
    const health = {
      status: "healthy",
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: process.env.NODE_ENV || "development",
      version: process.env.npm_package_version || "1.0.0",
      memory: {
        used: Math.round((process.memoryUsage().heapUsed / 1024 / 1024) * 100) / 100,
        total: Math.round((process.memoryUsage().heapTotal / 1024 / 1024) * 100) / 100,
        external: Math.round((process.memoryUsage().external / 1024 / 1024) * 100) / 100
      },
      cpu: {
        usage: process.cpuUsage()
      }
    };

    responseFormatter.success(res, "Service is healthy", health);
  } catch (error) {
    logger.error("Health check failed", { error: error.message });
    responseFormatter.error(res, "Health check failed", 503);
  }
});

/**
 * @desc    Detailed health check with database status
 * @route   GET /api/health/detailed
 * @access  Public
 */
router.get("/detailed", async (req, res) => {
  try {
    const dbHealth = await checkDatabaseHealth();
    
    const health = {
      status: dbHealth.status === "healthy" ? "healthy" : "degraded",
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: process.env.NODE_ENV || "development",
      version: process.env.npm_package_version || "1.0.0",
      services: {
        database: dbHealth,
        api: {
          status: "healthy",
          responseTime: Date.now() - req.startTime
        }
      },
      system: {
        memory: {
          used: Math.round((process.memoryUsage().heapUsed / 1024 / 1024) * 100) / 100,
          total: Math.round((process.memoryUsage().heapTotal / 1024 / 1024) * 100) / 100,
          external: Math.round((process.memoryUsage().external / 1024 / 1024) * 100) / 100,
          rss: Math.round((process.memoryUsage().rss / 1024 / 1024) * 100) / 100
        },
        cpu: process.cpuUsage(),
        platform: process.platform,
        arch: process.arch,
        nodeVersion: process.version
      }
    };

    const statusCode = health.status === "healthy" ? 200 : 503;
    responseFormatter.success(res, "Detailed health check completed", health, statusCode);
  } catch (error) {
    logger.error("Detailed health check failed", { error: error.message });
    responseFormatter.error(res, "Detailed health check failed", 503);
  }
});

/**
 * @desc    Database statistics (admin only)
 * @route   GET /api/health/database
 * @access  Private/Admin
 */
router.get("/database", async (req, res) => {
  try {
    // Simple IP-based admin check (enhance with proper auth in production)
    const clientIP = req.ip || req.connection.remoteAddress;
    const adminIPs = process.env.ADMIN_IP_WHITELIST 
      ? process.env.ADMIN_IP_WHITELIST.split(',')
      : ['127.0.0.1', '::1'];

    if (!adminIPs.includes(clientIP)) {
      logger.warn("Unauthorized database stats access attempt", { ip: clientIP });
      return responseFormatter.error(res, "Access denied", 403);
    }

    const dbHealth = await checkDatabaseHealth();
    const dbStats = await getDatabaseStats();

    const databaseInfo = {
      health: dbHealth,
      statistics: dbStats,
      timestamp: new Date().toISOString()
    };

    responseFormatter.success(res, "Database statistics retrieved", databaseInfo);
  } catch (error) {
    logger.error("Database statistics retrieval failed", { error: error.message });
    responseFormatter.error(res, "Failed to retrieve database statistics", 500);
  }
});

/**
 * @desc    Readiness probe (for Kubernetes/Docker)
 * @route   GET /api/health/ready
 * @access  Public
 */
router.get("/ready", async (req, res) => {
  try {
    const dbHealth = await checkDatabaseHealth();
    
    if (dbHealth.status === "healthy") {
      res.status(200).json({ status: "ready" });
    } else {
      res.status(503).json({ status: "not ready", reason: "database unavailable" });
    }
  } catch (error) {
    logger.error("Readiness check failed", { error: error.message });
    res.status(503).json({ status: "not ready", reason: "service error" });
  }
});

/**
 * @desc    Liveness probe (for Kubernetes/Docker)
 * @route   GET /api/health/live
 * @access  Public
 */
router.get("/live", (req, res) => {
  try {
    // Simple liveness check - if we can respond, we're alive
    res.status(200).json({ 
      status: "alive",
      timestamp: new Date().toISOString(),
      uptime: process.uptime()
    });
  } catch (error) {
    logger.error("Liveness check failed", { error: error.message });
    res.status(503).json({ status: "dead" });
  }
});

/**
 * @desc    Application metrics (basic)
 * @route   GET /api/health/metrics
 * @access  Private/Admin
 */
router.get("/metrics", async (req, res) => {
  try {
    // Simple IP-based admin check
    const clientIP = req.ip || req.connection.remoteAddress;
    const adminIPs = process.env.ADMIN_IP_WHITELIST 
      ? process.env.ADMIN_IP_WHITELIST.split(',')
      : ['127.0.0.1', '::1'];

    if (!adminIPs.includes(clientIP)) {
      logger.warn("Unauthorized metrics access attempt", { ip: clientIP });
      return responseFormatter.error(res, "Access denied", 403);
    }

    const metrics = {
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      cpu: process.cpuUsage(),
      environment: process.env.NODE_ENV || "development",
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch,
      pid: process.pid
    };

    responseFormatter.success(res, "Application metrics retrieved", metrics);
  } catch (error) {
    logger.error("Metrics retrieval failed", { error: error.message });
    responseFormatter.error(res, "Failed to retrieve metrics", 500);
  }
});

module.exports = router;
