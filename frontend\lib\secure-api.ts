/**
 * Secure API client using httpOnly cookies instead of localStorage tokens
 */

import { getCSRFToken, authRateLimiter } from './secure-auth';

// API configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000';

/**
 * Secure API request wrapper
 */
async function secureRequest(
  endpoint: string,
  options: RequestInit = {}
): Promise<Response> {
  const url = endpoint.startsWith('http') ? endpoint : `${API_BASE_URL}${endpoint}`;
  
  // Rate limiting check
  const clientId = 'client'; // In production, use a more specific identifier
  if (!authRateLimiter.isAllowed(clientId)) {
    throw new Error('Too many requests. Please try again later.');
  }

  const csrfToken = await getCSRFToken();
  
  const defaultHeaders = {
    'Content-Type': 'application/json',
    'X-CSRF-Token': csrfToken,
  };

  const mergedOptions: RequestInit = {
    ...options,
    credentials: 'include', // Include cookies
    headers: {
      ...defaultHeaders,
      ...options.headers,
    },
  };

  try {
    const response = await fetch(url, mergedOptions);
    
    // Handle authentication errors
    if (response.status === 401) {
      // Token expired or invalid, redirect to login
      if (typeof window !== 'undefined') {
        window.location.href = '/login';
      }
      throw new Error('Authentication required');
    }
    
    return response;
  } catch (error) {
    console.error('API request error:', error);
    throw error;
  }
}

/**
 * Secure authentication API
 */
export const secureAuthAPI = {
  async login(email: string, password: string) {
    const csrfToken = await getCSRFToken();
    const response = await fetch('/api/auth/login', {
      method: 'POST',
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': csrfToken,
      },
      body: JSON.stringify({ email, password }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Login failed');
    }

    return response.json();
  },

  async register(name: string, email: string, password: string) {
    const csrfToken = await getCSRFToken();
    const response = await fetch('/api/auth/register', {
      method: 'POST',
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': csrfToken,
      },
      body: JSON.stringify({ name, email, password }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Registration failed');
    }

    return response.json();
  },

  async logout() {
    const csrfToken = await getCSRFToken();
    const response = await fetch('/api/auth/logout', {
      method: 'POST',
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': csrfToken,
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Logout failed');
    }

    return response.json();
  },

  async getCurrentUser() {
    const response = await fetch('/api/auth/status', {
      method: 'GET',
      credentials: 'include',
    });

    if (!response.ok) {
      // If it's a 401, it just means not authenticated (not an error)
      if (response.status === 401) {
        return { success: false, isAuthenticated: false, user: null };
      }
      const error = await response.json();
      throw new Error(error.message || 'Failed to get current user');
    }

    return response.json();
  },
};

/**
 * Secure resume API
 */
export const secureResumeAPI = {
  async generateResume(data: any) {
    const response = await secureRequest('/api/generate/resume', {
      method: 'POST',
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Resume generation failed');
    }

    return response.json();
  },

  async getResumes(page = 1, limit = 10) {
    const response = await secureRequest(`/api/generate/resume?page=${page}&limit=${limit}`);

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch resumes');
    }

    return response.json();
  },

  async getResume(id: string) {
    const response = await secureRequest(`/api/generate/resume/${id}`);

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch resume');
    }

    return response.json();
  },

  async updateResume(id: string, data: any) {
    const response = await secureRequest(`/api/generate/resume/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Resume update failed');
    }

    return response.json();
  },

  async deleteResume(id: string) {
    const response = await secureRequest(`/api/generate/resume/${id}`, {
      method: 'DELETE',
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Resume deletion failed');
    }

    return response.json();
  },
};

/**
 * Secure cover letter API
 */
export const secureCoverLetterAPI = {
  async generateCoverLetter(data: any) {
    const response = await secureRequest('/api/generate/cover-letter', {
      method: 'POST',
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Cover letter generation failed');
    }

    return response.json();
  },

  async getCoverLetters(page = 1, limit = 10) {
    const response = await secureRequest(`/api/generate/cover-letter?page=${page}&limit=${limit}`);

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch cover letters');
    }

    return response.json();
  },

  async getCoverLetter(id: string) {
    const response = await secureRequest(`/api/generate/cover-letter/${id}`);

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch cover letter');
    }

    return response.json();
  },

  async updateCoverLetter(id: string, data: any) {
    const response = await secureRequest(`/api/generate/cover-letter/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Cover letter update failed');
    }

    return response.json();
  },

  async deleteCoverLetter(id: string) {
    const response = await secureRequest(`/api/generate/cover-letter/${id}`, {
      method: 'DELETE',
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Cover letter deletion failed');
    }

    return response.json();
  },
};

/**
 * Secure LinkedIn API
 */
export const secureLinkedInAPI = {
  async generateLinkedInBio(data: any) {
    const response = await secureRequest('/api/generate/linkedin', {
      method: 'POST',
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'LinkedIn bio generation failed');
    }

    return response.json();
  },

  async getLinkedInBios(page = 1, limit = 10) {
    const response = await secureRequest(`/api/generate/linkedin?page=${page}&limit=${limit}`);

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch LinkedIn bios');
    }

    return response.json();
  },

  async getLinkedInBio(id: string) {
    const response = await secureRequest(`/api/generate/linkedin/${id}`);

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch LinkedIn bio');
    }

    return response.json();
  },

  async updateLinkedInBio(id: string, data: any) {
    const response = await secureRequest(`/api/generate/linkedin/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'LinkedIn bio update failed');
    }

    return response.json();
  },

  async deleteLinkedInBio(id: string) {
    const response = await secureRequest(`/api/generate/linkedin/${id}`, {
      method: 'DELETE',
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'LinkedIn bio deletion failed');
    }

    return response.json();
  },
};

/**
 * Secure export API
 */
export const secureExportAPI = {
  async exportToPDF(type: string, id: string) {
    const response = await secureRequest(`/api/export/pdf/${type}/${id}`, {
      method: 'GET',
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'PDF export failed');
    }

    return response.blob();
  },

  async exportToWord(type: string, id: string) {
    const response = await secureRequest(`/api/export/docx/${type}/${id}`, {
      method: 'GET',
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Word export failed');
    }

    return response.blob();
  },
};

/**
 * Secure user API
 */
export const secureUserAPI = {
  async getProfile() {
    const response = await secureRequest('/api/users/profile');

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch profile');
    }

    return response.json();
  },

  async updateProfile(data: any) {
    const response = await secureRequest('/api/users/profile', {
      method: 'PUT',
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Profile update failed');
    }

    return response.json();
  },

  async getUsageStats() {
    const response = await secureRequest('/api/users/usage');

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch usage stats');
    }

    return response.json();
  },

  async updatePassword(data: any) {
    const response = await secureRequest('/api/users/password', {
      method: 'PUT',
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Password update failed');
    }

    return response.json();
  },
};

/**
 * Secure payment API
 */
export const securePaymentAPI = {
  async getPlans() {
    const response = await secureRequest('/api/payment/plans');

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch plans');
    }

    return response.json();
  },

  async createOrder(data: any) {
    const response = await secureRequest('/api/payment/create-order', {
      method: 'POST',
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to create order');
    }

    return response.json();
  },

  async verifyPayment(data: any) {
    const response = await secureRequest('/api/payment/verify', {
      method: 'POST',
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Payment verification failed');
    }

    return response.json();
  },
};
