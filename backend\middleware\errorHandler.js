/**
 * Error handling middleware
 */
const logger = require("../utils/logger");

const errorHandler = (err, req, res, next) => {
  let error = { ...err };
  error.message = err.message;

  // Log error with proper logging
  logger.error("Application error", {
    error: err.message,
    stack: err.stack,
    url: req.originalUrl,
    method: req.method,
    userId: req.user?.id,
  });

  // Mongoose bad ObjectId
  if (err.name === "CastError") {
    const message = "Resource not found";
    error = { message, statusCode: 404 };
  }

  // Mongoose duplicate key
  if (err.code === 11000) {
    const message = "Duplicate field value entered";
    error = { message, statusCode: 400 };
  }

  // Mongoose validation error
  if (err.name === "ValidationError") {
    const message = Object.values(err.errors).map((val) => val.message);
    error = { message, statusCode: 400 };
  }

  // JWT errors
  if (err.name === "JsonWebTokenError") {
    const message = "Invalid token";
    error = { message, statusCode: 401 };
  }

  if (err.name === "TokenExpiredError") {
    const message = "Token expired";
    error = { message, statusCode: 401 };
  }

  // External API errors (Gemini, OpenAI, etc.)
  if (err.name === "OpenAIError" || err.response) {
    if (err.response && err.response.status === 429) {
      error = {
        message: "Service temporarily unavailable due to high demand",
        statusCode: 503,
      };
    } else if (err.response && err.response.status === 401) {
      error = { message: "Service configuration error", statusCode: 500 };
    } else {
      error = {
        message: "AI service temporarily unavailable",
        statusCode: 503,
      };
    }
  }

  // Production-safe response
  const statusCode = error.statusCode || 500;
  const response = {
    success: false,
    message: error.message || "Internal server error",
    ...(process.env.NODE_ENV === "development" && {
      error: err.message,
      stack: err.stack,
    }),
  };

  res.status(statusCode).json(response);
};

module.exports = errorHandler;
