# CareerPilotAI Backend Environment Configuration
# Copy this file to .env and update with your actual values

# Server Configuration
PORT=5000
NODE_ENV=development

# Database Configuration
# MongoDB connection string - replace with your MongoDB Atlas or local MongoDB URL
MONGO_URI=mongodb+srv://username:<EMAIL>/CareerPilotAI?retryWrites=true&w=majority

# JWT Authentication Configuration
# Use a strong, unique secret for production
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRE=30d

# AI Service Configuration
# OpenAI API (legacy support) - Get from https://platform.openai.com/api-keys
OPENAI_API_KEY=your_openai_api_key

# Google Gemini API - Get a valid API key from https://aistudio.google.com/app/apikey
GEMINI_API_KEY=your_gemini_api_key

# Razorpay Payment Gateway Configuration
# Get these credentials from your Razorpay Dashboard: https://dashboard.razorpay.com/app/keys

# For testing, use test credentials (starts with rzp_test_)
# Example: rzp_test_1234567890abcdef
RAZORPAY_KEY_ID=rzp_test_your_key_id_here

# Razorpay Key Secret (keep this secure!)
RAZORPAY_KEY_SECRET=your_razorpay_key_secret_here

# Webhook secret for verifying webhook signatures
# Set this in Razorpay Dashboard > Settings > Webhooks
# Use a strong random string
RAZORPAY_WEBHOOK_SECRET=your_webhook_secret_here

# Security Configuration
# CORS allowed origins (comma-separated)
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001

# Rate limiting (requests per 15 minutes)
RATE_LIMIT_MAX=100

# Admin IP whitelist (optional, comma-separated)
ADMIN_IP_WHITELIST=127.0.0.1,::1

# =============================================================================
# SETUP INSTRUCTIONS:
# =============================================================================
# 
# 1. MongoDB Setup:
#    - Create a MongoDB Atlas account or use local MongoDB
#    - Replace MONGO_URI with your connection string
#
# 2. Gemini AI Setup:
#    - Go to https://aistudio.google.com/app/apikey
#    - Create a new API key
#    - Replace GEMINI_API_KEY with your key
#
# 3. Razorpay Setup:
#    - Sign up at https://razorpay.com
#    - Complete KYC verification
#    - Go to Dashboard > Settings > API Keys
#    - Generate test/live keys and replace the values above
#    - Set up webhooks in Dashboard > Settings > Webhooks
#    - Webhook URL: https://yourdomain.com/api/payment/webhook
#    - Events to subscribe: payment.captured
#
# 4. JWT Secret:
#    - Generate a strong random string for JWT_SECRET
#    - You can use: node -e "console.log(require('crypto').randomBytes(64).toString('hex'))"
#
# 5. Security Notes:
#    - Never commit .env file to version control
#    - Use different secrets for development and production
#    - Keep Razorpay secrets secure and rotate them periodically
# =============================================================================
