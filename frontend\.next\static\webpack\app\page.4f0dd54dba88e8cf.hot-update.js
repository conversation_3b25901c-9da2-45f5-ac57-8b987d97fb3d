"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./lib/auth-context.tsx":
/*!******************************!*\
  !*** ./lib/auth-context.tsx ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: function() { return /* binding */ AuthProvider; },\n/* harmony export */   useAuth: function() { return /* binding */ useAuth; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_secure_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/secure-api */ \"(app-pages-browser)/./lib/secure-api.ts\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _lib_secure_auth__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/secure-auth */ \"(app-pages-browser)/./lib/secure-auth.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider(param) {\n    let { children } = param;\n    _s();\n    const [authState, setAuthState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        user: null,\n        isAuthenticated: false,\n        loading: true,\n        error: null\n    });\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__.useToast)();\n    // Load user on initial render using secure authentication\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadUser = async ()=>{\n            try {\n                const res = await _lib_secure_api__WEBPACK_IMPORTED_MODULE_3__.secureAuthAPI.getCurrentUser();\n                if (res.success && res.isAuthenticated) {\n                    setAuthState({\n                        user: res.user,\n                        isAuthenticated: true,\n                        loading: false,\n                        error: null\n                    });\n                } else {\n                    // Not authenticated, but not an error\n                    setAuthState({\n                        user: null,\n                        isAuthenticated: false,\n                        loading: false,\n                        error: null\n                    });\n                }\n            } catch (err) {\n                console.error(\"Error loading user:\", err);\n                setAuthState({\n                    user: null,\n                    isAuthenticated: false,\n                    loading: false,\n                    error: null\n                });\n            }\n        };\n        loadUser();\n    }, []);\n    // Register user with enhanced security\n    const register = async (name, email, password)=>{\n        setAuthState((prev)=>({\n                ...prev,\n                loading: true,\n                error: null\n            }));\n        try {\n            // Client-side validation\n            if (!name.trim()) {\n                throw new Error(\"Name is required\");\n            }\n            if (!(0,_lib_secure_auth__WEBPACK_IMPORTED_MODULE_5__.isValidEmail)(email)) {\n                throw new Error(\"Please enter a valid email address\");\n            }\n            const passwordValidation = (0,_lib_secure_auth__WEBPACK_IMPORTED_MODULE_5__.validatePassword)(password);\n            if (!passwordValidation.isValid) {\n                throw new Error(passwordValidation.errors[0]);\n            }\n            // Rate limiting check\n            if (!_lib_secure_auth__WEBPACK_IMPORTED_MODULE_5__.authRateLimiter.isAllowed(\"register\")) {\n                throw new Error(\"Too many registration attempts. Please try again later.\");\n            }\n            const res = await _lib_secure_api__WEBPACK_IMPORTED_MODULE_3__.secureAuthAPI.register(name, email, password);\n            // Update auth state with user data (no token stored in localStorage)\n            setAuthState({\n                user: res.user,\n                isAuthenticated: true,\n                loading: false,\n                error: null\n            });\n            toast({\n                title: \"Registration successful\",\n                description: \"Welcome to CareerPilotAI!\"\n            });\n            router.push(\"/dashboard\");\n        } catch (err) {\n            console.error(\"Registration error:\", err);\n            setAuthState((prev)=>({\n                    ...prev,\n                    loading: false,\n                    error: err.message || \"Registration failed\"\n                }));\n            toast({\n                title: \"Registration failed\",\n                description: err.message || \"Please try again\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    // Login user with enhanced security\n    const login = async (email, password)=>{\n        setAuthState((prev)=>({\n                ...prev,\n                loading: true,\n                error: null\n            }));\n        try {\n            // Client-side validation\n            if (!(0,_lib_secure_auth__WEBPACK_IMPORTED_MODULE_5__.isValidEmail)(email)) {\n                throw new Error(\"Please enter a valid email address\");\n            }\n            if (!password.trim()) {\n                throw new Error(\"Password is required\");\n            }\n            // Rate limiting check\n            if (!_lib_secure_auth__WEBPACK_IMPORTED_MODULE_5__.authRateLimiter.isAllowed(\"login\")) {\n                throw new Error(\"Too many login attempts. Please try again later.\");\n            }\n            const res = await _lib_secure_api__WEBPACK_IMPORTED_MODULE_3__.secureAuthAPI.login(email, password);\n            // Update auth state with user data (no token stored in localStorage)\n            setAuthState({\n                user: res.user,\n                isAuthenticated: true,\n                loading: false,\n                error: null\n            });\n            toast({\n                title: \"Login successful\",\n                description: \"Welcome back!\"\n            });\n            router.push(\"/dashboard/dashboard\");\n        } catch (err) {\n            console.error(\"Login error:\", err);\n            setAuthState((prev)=>({\n                    ...prev,\n                    loading: false,\n                    error: err.message || \"Login failed\"\n                }));\n            toast({\n                title: \"Login failed\",\n                description: err.message || \"Invalid credentials\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    // Logout user with secure cleanup\n    const logout = async ()=>{\n        try {\n            await _lib_secure_api__WEBPACK_IMPORTED_MODULE_3__.secureAuthAPI.logout();\n        } catch (err) {\n            console.error(\"Logout error:\", err);\n        } finally{\n            // Clear auth state (httpOnly cookie is cleared by the API)\n            setAuthState({\n                user: null,\n                isAuthenticated: false,\n                loading: false,\n                error: null\n            });\n            toast({\n                title: \"Logged out\",\n                description: \"You have been logged out successfully\"\n            });\n            router.push(\"/auth/login\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            ...authState,\n            login,\n            register,\n            logout\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Dev Ideas\\\\CareerPilotAI-2\\\\frontend\\\\lib\\\\auth-context.tsx\",\n        lineNumber: 199,\n        columnNumber: 5\n    }, this);\n}\n_s(AuthProvider, \"zonBXiT/NH4zavR/CKlhbj44XN0=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__.useToast\n    ];\n});\n_c = AuthProvider;\nfunction useAuth() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n_s1(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/auth-context.tsx\n"));

/***/ })

});