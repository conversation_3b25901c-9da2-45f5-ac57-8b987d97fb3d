const mongoose = require("mongoose");
const bcrypt = require("bcryptjs");
const jwt = require("jsonwebtoken");
const config = require("../config/config");
const logger = require("../utils/logger");

const UserSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: [true, "Please add a name"],
      trim: true,
      maxlength: [50, "Name cannot be more than 50 characters"],
    },
    email: {
      type: String,
      required: [true, "Please add an email"],
      unique: true,
      match: [
        /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/,
        "Please add a valid email",
      ],
    },
    password: {
      type: String,
      required: [true, "Please add a password"],
      minlength: [6, "Password must be at least 6 characters"],
      select: false,
    },
    planType: {
      type: String,
      enum: ["free", "basic", "premium"],
      default: "free",
    },
    usageStats: {
      resumeGenerations: {
        type: Number,
        default: 0,
      },
      coverLetterGenerations: {
        type: Number,
        default: 0,
      },
      linkedinGenerations: {
        type: Number,
        default: 0,
      },
    },
    resetPasswordToken: String,
    resetPasswordExpire: Date,
    lastLogin: {
      type: Date,
      default: null,
    },
    loginAttempts: {
      type: Number,
      default: 0,
    },
    lockUntil: Date,
    isActive: {
      type: Boolean,
      default: true,
    },
    createdAt: {
      type: Date,
      default: Date.now,
    },
    updatedAt: {
      type: Date,
      default: Date.now,
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  }
);

// Indexes for performance optimization (email index is automatic due to unique: true)
UserSchema.index({ planType: 1 });
UserSchema.index({ createdAt: -1 });
UserSchema.index({ lastLogin: -1 });
UserSchema.index({ isActive: 1 });

// Compound indexes for common queries
UserSchema.index({ email: 1, isActive: 1 });
UserSchema.index({ planType: 1, isActive: 1 });

// Virtual for account lock status
UserSchema.virtual("isLocked").get(function () {
  return !!(this.lockUntil && this.lockUntil > Date.now());
});

// Virtual for remaining login attempts
UserSchema.virtual("remainingAttempts").get(function () {
  const maxAttempts = 5;
  if (this.isLocked) return 0;
  return Math.max(0, maxAttempts - this.loginAttempts);
});

// Encrypt password using bcrypt
UserSchema.pre("save", async function (next) {
  if (!this.isModified("password")) {
    return next();
  }

  try {
    const salt = await bcrypt.genSalt(12); // Increased salt rounds for better security
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    logger.error("Error hashing password", {
      error: error.message,
      userId: this._id,
    });
    next(error);
  }
});

// Update timestamps on save
UserSchema.pre("save", function (next) {
  if (!this.isNew) {
    this.updatedAt = Date.now();
  }
  next();
});

// Sign JWT and return
UserSchema.methods.getSignedJwtToken = function () {
  return jwt.sign({ id: this._id }, config.jwt.secret, {
    expiresIn: config.jwt.expiresIn,
  });
};

// Match user entered password to hashed password in database
UserSchema.methods.matchPassword = async function (enteredPassword) {
  return await bcrypt.compare(enteredPassword, this.password);
};

// Check if user has reached usage limits
UserSchema.methods.hasReachedLimit = function (generationType) {
  if (this.planType === "premium") {
    return false; // Premium users have unlimited access
  }

  const plan = config.plans[this.planType];
  const currentUsage = this.usageStats[`${generationType}Generations`];
  const limit = plan.features[`${generationType}Generations`];

  return currentUsage >= limit;
};

// Get plan limit for a specific feature
UserSchema.methods.getPlanLimit = function (feature) {
  const plan = config.plans[this.planType];
  return plan.features[feature] || 0;
};

// Check if user's plan allows a specific export format
UserSchema.methods.canUseExportFormat = function (format) {
  const plan = config.plans[this.planType];
  const featureKey = `${format}Export`;
  return plan.features[featureKey] === true;
};

// Increment usage counter
UserSchema.methods.incrementUsage = function (generationType) {
  this.usageStats[`${generationType}Generations`] += 1;
  return this.save();
};

// Account lockout methods for brute force protection
UserSchema.methods.incLoginAttempts = function () {
  const maxAttempts = 5;
  const lockTime = 2 * 60 * 60 * 1000; // 2 hours

  // If we have a previous lock that has expired, restart at 1
  if (this.lockUntil && this.lockUntil < Date.now()) {
    return this.updateOne({
      $unset: { lockUntil: 1 },
      $set: { loginAttempts: 1 },
    });
  }

  const updates = { $inc: { loginAttempts: 1 } };

  // If we have hit max attempts and it's not locked yet, lock the account
  if (this.loginAttempts + 1 >= maxAttempts && !this.isLocked) {
    updates.$set = { lockUntil: Date.now() + lockTime };
    logger.warn("User account locked due to failed login attempts", {
      userId: this._id,
      email: this.email,
      attempts: this.loginAttempts + 1,
    });
  }

  return this.updateOne(updates);
};

UserSchema.methods.resetLoginAttempts = function () {
  return this.updateOne({
    $unset: { loginAttempts: 1, lockUntil: 1 },
    $set: { lastLogin: Date.now() },
  });
};

// Static method to find active users
UserSchema.statics.findActive = function (conditions = {}) {
  return this.find({ ...conditions, isActive: true });
};

// Static method for secure user lookup
UserSchema.statics.findByEmailSecure = function (email) {
  return this.findOne({
    email: email.toLowerCase().trim(),
    isActive: true,
  }).select("+password");
};

module.exports = mongoose.model("User", UserSchema);
