const express = require("express");
const router = express.Router();
const {
  register,
  login,
  getMe,
  logout,
} = require("../controllers/authController");
const { protect } = require("../middleware/auth");
const {
  registerValidation,
  loginValidation,
  validate,
} = require("../middleware/validator");
const crypto = require("crypto");

// CSRF token endpoint
router.get("/csrf-token", (req, res) => {
  // Generate a simple CSRF token for development
  const token = crypto.randomBytes(32).toString("hex");
  res.json({
    success: true,
    token: token,
  });
});

// Public routes
router.post("/register", registerValidation, validate, register);
router.post("/login", loginValidation, validate, login);

// Protected routes
router.get("/me", protect, getMe);
router.get("/logout", protect, logout);

module.exports = router;
