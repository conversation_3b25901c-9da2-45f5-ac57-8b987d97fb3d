/**
 * Secure authentication utilities for client-side use
 * This replaces the vulnerable localStorage token storage
 */

// CSRF token management
let csrfToken: string | null = null;

/**
 * Generate a random CSRF token
 */
export function generateCSRFToken(): string {
  const array = new Uint8Array(32);
  crypto.getRandomValues(array);
  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
}

/**
 * Get CSRF token from server or generate new one
 */
export async function getCSRFToken(): Promise<string> {
  // In development, use a simple bypass token
  if (process.env.NODE_ENV === 'development') {
    return 'dev-bypass';
  }

  if (!csrfToken) {
    try {
      // Try to get CSRF token from server
      const response = await fetch('/api/auth/csrf-token', {
        method: 'GET',
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        csrfToken = data.token;
      } else {
        // Fallback: generate client-side token
        csrfToken = generateCSRFToken();
      }
    } catch (error) {
      // Fallback: generate client-side token
      csrfToken = generateCSRFToken();
    }
  }
  return csrfToken;
}

/**
 * Validate CSRF token (server-side validation)
 */
export function validateCSRFToken(token: string): boolean {
  return token === csrfToken;
}

/**
 * These functions are moved to server-side utilities
 * Client-side code should use the secure API endpoints instead
 */

/**
 * Client-side authentication status check
 * This uses a secure API endpoint instead of direct token access
 */
export async function checkAuthStatus(): Promise<{
  isAuthenticated: boolean;
  user?: any;
  error?: string;
}> {
  try {
    const response = await fetch('/api/auth/status', {
      method: 'GET',
      credentials: 'include', // Include cookies
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (response.ok) {
      const data = await response.json();
      return {
        isAuthenticated: true,
        user: data.user,
      };
    } else {
      return {
        isAuthenticated: false,
        error: 'Authentication check failed',
      };
    }
  } catch (error) {
    console.error('Auth status check error:', error);
    return {
      isAuthenticated: false,
      error: 'Network error during authentication check',
    };
  }
}

/**
 * Secure login function
 */
export async function secureLogin(email: string, password: string): Promise<{
  success: boolean;
  user?: any;
  error?: string;
}> {
  try {
    const csrfToken = await getCSRFToken();

    const response = await fetch('/api/auth/login', {
      method: 'POST',
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': csrfToken,
      },
      body: JSON.stringify({ email, password }),
    });

    const data = await response.json();

    if (response.ok) {
      return {
        success: true,
        user: data.user,
      };
    } else {
      return {
        success: false,
        error: data.message || 'Login failed',
      };
    }
  } catch (error) {
    console.error('Login error:', error);
    return {
      success: false,
      error: 'Network error during login',
    };
  }
}

/**
 * Secure logout function
 */
export async function secureLogout(): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    const csrfToken = await getCSRFToken();

    const response = await fetch('/api/auth/logout', {
      method: 'POST',
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': csrfToken,
      },
    });

    if (response.ok) {
      return { success: true };
    } else {
      const data = await response.json();
      return {
        success: false,
        error: data.message || 'Logout failed',
      };
    }
  } catch (error) {
    console.error('Logout error:', error);
    return {
      success: false,
      error: 'Network error during logout',
    };
  }
}

/**
 * Secure API request wrapper
 */
export async function secureApiRequest(
  url: string,
  options: RequestInit = {}
): Promise<Response> {
  const csrfToken = await getCSRFToken();

  const defaultHeaders = {
    'Content-Type': 'application/json',
    'X-CSRF-Token': csrfToken,
  };

  const mergedOptions: RequestInit = {
    ...options,
    credentials: 'include',
    headers: {
      ...defaultHeaders,
      ...options.headers,
    },
  };

  return fetch(url, mergedOptions);
}

/**
 * Input sanitization for XSS prevention
 */
export function sanitizeInput(input: string): string {
  const div = document.createElement('div');
  div.textContent = input;
  return div.innerHTML;
}

/**
 * Validate email format
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Validate password strength
 */
export function validatePassword(password: string): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];
  
  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long');
  }
  
  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }
  
  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }
  
  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  }
  
  if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    errors.push('Password must contain at least one special character');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * Rate limiting for client-side requests
 */
class RateLimiter {
  private requests: Map<string, number[]> = new Map();
  private readonly maxRequests: number;
  private readonly windowMs: number;

  constructor(maxRequests: number = 5, windowMs: number = 60000) {
    this.maxRequests = maxRequests;
    this.windowMs = windowMs;
  }

  isAllowed(key: string): boolean {
    const now = Date.now();
    const requests = this.requests.get(key) || [];
    
    // Remove old requests outside the window
    const validRequests = requests.filter(time => now - time < this.windowMs);
    
    if (validRequests.length >= this.maxRequests) {
      return false;
    }
    
    validRequests.push(now);
    this.requests.set(key, validRequests);
    return true;
  }
}

export const authRateLimiter = new RateLimiter(5, 60000); // 5 requests per minute
