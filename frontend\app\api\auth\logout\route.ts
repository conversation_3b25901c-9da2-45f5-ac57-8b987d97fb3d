/**
 * Secure logout API route
 */
import { NextRequest, NextResponse } from 'next/server';
import { getAuthCookie, clearAuthCookie } from '@/lib/server-auth';
import { csrfValidator } from '@/app/api/csrf-token/route';

export async function POST(request: NextRequest) {
  try {
    // CSRF protection
    const csrfToken = request.headers.get('X-CSRF-Token');
    const sessionId = request.cookies.get('session-id')?.value;

    // In development, allow bypass, in production require proper validation
    if (process.env.NODE_ENV === 'production') {
      if (!csrfToken || !sessionId || !csrfValidator(csrfToken, sessionId)) {
        return NextResponse.json(
          { success: false, message: 'Invalid CSRF token' },
          { status: 403 }
        );
      }
    } else {
      // Development mode: just check if token exists
      if (!csrfToken) {
        return NextResponse.json(
          { success: false, message: 'CSRF token required' },
          { status: 403 }
        );
      }
    }

    const token = getAuthCookie();

    if (token) {
      // Forward logout request to backend with token
      try {
        await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/auth/logout`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
          },
        });
      } catch (error) {
        console.error('Backend logout error:', error);
        // Continue with cookie deletion even if backend fails
      }
    }

    // Clear the httpOnly cookie
    clearAuthCookie();

    return NextResponse.json({
      success: true,
      message: 'Logged out successfully',
    });
  } catch (error) {
    console.error('Logout API error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Handle preflight requests
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': process.env.NEXT_PUBLIC_FRONTEND_URL || 'http://localhost:3000',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, X-CSRF-Token',
      'Access-Control-Allow-Credentials': 'true',
    },
  });
}
