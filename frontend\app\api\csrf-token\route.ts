/**
 * CSRF token generation endpoint
 */
import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import crypto from 'crypto';

// Store CSRF tokens temporarily (in production, use Redis or database)
const csrfTokens = new Map<string, { token: string; expires: number }>();

/**
 * Generate a secure CSRF token
 */
function generateCSRFToken(): string {
  return crypto.randomBytes(32).toString('hex');
}

/**
 * Get or create CSRF token for the session
 */
export async function GET(request: NextRequest) {
  try {
    const cookieStore = cookies();
    
    // Get session identifier (could be from auth token or create a session ID)
    let sessionId = cookieStore.get('session-id')?.value;
    
    if (!sessionId) {
      // Create a new session ID
      sessionId = crypto.randomBytes(16).toString('hex');
      cookieStore.set('session-id', sessionId, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 24 * 60 * 60, // 24 hours
        path: '/',
      });
    }

    // Check if we have a valid CSRF token for this session
    const existingToken = csrfTokens.get(sessionId);
    const now = Date.now();

    if (existingToken && existingToken.expires > now) {
      return NextResponse.json({
        success: true,
        token: existingToken.token,
      });
    }

    // Generate new CSRF token
    const newToken = generateCSRFToken();
    const expires = now + (60 * 60 * 1000); // 1 hour

    csrfTokens.set(sessionId, {
      token: newToken,
      expires,
    });

    // Clean up expired tokens
    for (const [key, value] of csrfTokens.entries()) {
      if (value.expires <= now) {
        csrfTokens.delete(key);
      }
    }

    return NextResponse.json({
      success: true,
      token: newToken,
    });
  } catch (error) {
    console.error('CSRF token generation error:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to generate CSRF token' },
      { status: 500 }
    );
  }
}

/**
 * Validate CSRF token
 */
export function validateCSRFToken(token: string, sessionId: string): boolean {
  // In development, allow a simple bypass for testing
  if (process.env.NODE_ENV === 'development' && token === 'dev-bypass') {
    return true;
  }

  const storedToken = csrfTokens.get(sessionId);

  if (!storedToken) {
    return false;
  }

  if (storedToken.expires <= Date.now()) {
    csrfTokens.delete(sessionId);
    return false;
  }

  return storedToken.token === token;
}

// Export the validation function for use in other API routes
export { validateCSRFToken as csrfValidator };
