/**
 * Production-ready logging utility
 * Replaces console.log statements with proper logging levels
 */

const isDevelopment = process.env.NODE_ENV === "development";
const isProduction = process.env.NODE_ENV === "production";

/**
 * Log levels for different types of messages
 */
const LOG_LEVELS = {
  ERROR: "ERROR",
  WARN: "WARN",
  INFO: "INFO",
  DEBUG: "DEBUG",
};

/**
 * Sanitize sensitive data from logs
 * @param {any} data - Data to sanitize
 * @returns {any} - Sanitized data
 */
const sanitizeLogData = (data) => {
  if (typeof data !== "object" || data === null) {
    return data;
  }

  const sensitiveFields = [
    "password",
    "token",
    "apiKey",
    "secret",
    "authorization",
    "cookie",
    "session",
    "jwt",
    "bearer",
    "key",
    "credential",
  ];

  const sanitized = { ...data };

  const sanitizeObject = (obj, path = "") => {
    for (const key in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, key)) {
        const lowerKey = key.toLowerCase();
        const fullPath = path ? `${path}.${key}` : key;

        if (sensitiveFields.some((field) => lowerKey.includes(field))) {
          obj[key] = "[REDACTED]";
        } else if (typeof obj[key] === "object" && obj[key] !== null) {
          sanitizeObject(obj[key], fullPath);
        } else if (typeof obj[key] === "string" && obj[key].length > 50) {
          // Truncate long strings that might contain sensitive data
          if (lowerKey.includes("email")) {
            obj[key] =
              obj[key].substring(0, 3) + "***@" + obj[key].split("@")[1];
          } else {
            obj[key] = obj[key].substring(0, 20) + "...[TRUNCATED]";
          }
        }
      }
    }
  };

  sanitizeObject(sanitized);
  return sanitized;
};

/**
 * Format log message with timestamp and level
 * @param {string} level - Log level
 * @param {string} message - Log message
 * @param {any} data - Additional data
 * @returns {string} - Formatted log message
 */
const formatLogMessage = (level, message, data = null) => {
  const timestamp = new Date().toISOString();
  const sanitizedData = data ? sanitizeLogData(data) : null;

  const logEntry = {
    timestamp,
    level,
    message,
    ...(sanitizedData && { data: sanitizedData }),
    ...(isProduction && { environment: "production" }),
  };

  return JSON.stringify(logEntry);
};

/**
 * Logger class with different log levels
 */
class Logger {
  /**
   * Log error messages
   * @param {string} message - Error message
   * @param {any} error - Error object or additional data
   */
  error(message, error = null) {
    const logMessage = formatLogMessage(LOG_LEVELS.ERROR, message, error);
    console.error(logMessage);
  }

  /**
   * Log warning messages
   * @param {string} message - Warning message
   * @param {any} data - Additional data
   */
  warn(message, data = null) {
    const logMessage = formatLogMessage(LOG_LEVELS.WARN, message, data);
    console.warn(logMessage);
  }

  /**
   * Log info messages
   * @param {string} message - Info message
   * @param {any} data - Additional data
   */
  info(message, data = null) {
    const logMessage = formatLogMessage(LOG_LEVELS.INFO, message, data);
    console.log(logMessage);
  }

  /**
   * Log debug messages (only in development)
   * @param {string} message - Debug message
   * @param {any} data - Additional data
   */
  debug(message, data = null) {
    if (isDevelopment) {
      const logMessage = formatLogMessage(LOG_LEVELS.DEBUG, message, data);
      console.log(logMessage);
    }
  }

  /**
   * Log authentication events
   * @param {string} event - Auth event type
   * @param {string} userId - User ID
   * @param {any} metadata - Additional metadata
   */
  auth(event, userId = null, metadata = null) {
    const message = `Auth Event: ${event}`;
    const data = {
      userId: userId || "[ANONYMOUS]",
      event,
      ...(metadata && { metadata: sanitizeLogData(metadata) }),
    };
    this.info(message, data);
  }

  /**
   * Log API requests
   * @param {string} method - HTTP method
   * @param {string} path - Request path
   * @param {string} userId - User ID
   * @param {number} statusCode - Response status code
   * @param {number} duration - Request duration in ms
   */
  request(method, path, userId = null, statusCode = null, duration = null) {
    const message = `API Request: ${method} ${path}`;
    const data = {
      method,
      path,
      userId: userId || "[ANONYMOUS]",
      ...(statusCode && { statusCode }),
      ...(duration && { duration: `${duration}ms` }),
    };
    this.info(message, data);
  }

  /**
   * Log database operations
   * @param {string} operation - Database operation
   * @param {string} collection - Collection/table name
   * @param {any} metadata - Additional metadata
   */
  database(operation, collection, metadata = null) {
    const message = `Database: ${operation} on ${collection}`;
    const data = {
      operation,
      collection,
      ...(metadata && { metadata: sanitizeLogData(metadata) }),
    };
    this.debug(message, data);
  }

  /**
   * Log external API calls
   * @param {string} service - External service name
   * @param {string} operation - Operation performed
   * @param {boolean} success - Whether the operation was successful
   * @param {any} metadata - Additional metadata
   */
  external(service, operation, success, metadata = null) {
    const message = `External API: ${service} - ${operation} - ${
      success ? "SUCCESS" : "FAILED"
    }`;
    const data = {
      service,
      operation,
      success,
      ...(metadata && { metadata: sanitizeLogData(metadata) }),
    };

    if (success) {
      this.info(message, data);
    } else {
      this.warn(message, data);
    }
  }
}

// Create singleton logger instance
const logger = new Logger();

module.exports = logger;
