"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/register/route";
exports.ids = ["app/api/auth/register/route"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Fregister%2Froute&page=%2Fapi%2Fauth%2Fregister%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fregister%2Froute.ts&appDir=D%3A%5CDev%20Ideas%5CCareerPilotAI-2%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CDev%20Ideas%5CCareerPilotAI-2%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Fregister%2Froute&page=%2Fapi%2Fauth%2Fregister%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fregister%2Froute.ts&appDir=D%3A%5CDev%20Ideas%5CCareerPilotAI-2%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CDev%20Ideas%5CCareerPilotAI-2%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_Dev_Ideas_CareerPilotAI_2_frontend_app_api_auth_register_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/auth/register/route.ts */ \"(rsc)/./app/api/auth/register/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/register/route\",\n        pathname: \"/api/auth/register\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/register/route\"\n    },\n    resolvedPagePath: \"D:\\\\Dev Ideas\\\\CareerPilotAI-2\\\\frontend\\\\app\\\\api\\\\auth\\\\register\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_Dev_Ideas_CareerPilotAI_2_frontend_app_api_auth_register_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/auth/register/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Fregister%2Froute&page=%2Fapi%2Fauth%2Fregister%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fregister%2Froute.ts&appDir=D%3A%5CDev%20Ideas%5CCareerPilotAI-2%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CDev%20Ideas%5CCareerPilotAI-2%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/auth/register/route.ts":
/*!****************************************!*\
  !*** ./app/api/auth/register/route.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OPTIONS: () => (/* binding */ OPTIONS),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_server_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/server-auth */ \"(rsc)/./lib/server-auth.ts\");\n/* harmony import */ var _app_api_csrf_token_route__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/api/csrf-token/route */ \"(rsc)/./app/api/csrf-token/route.ts\");\n/**\n * Secure registration API route with httpOnly cookies\n */ \n\n\nasync function POST(request) {\n    try {\n        // CSRF protection\n        const csrfToken = request.headers.get(\"X-CSRF-Token\");\n        const sessionId = request.cookies.get(\"session-id\")?.value;\n        // In development, allow bypass, in production require proper validation\n        if (false) {} else {\n            // Development mode: just check if token exists\n            if (!csrfToken) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: false,\n                    message: \"CSRF token required\"\n                }, {\n                    status: 403\n                });\n            }\n        }\n        const body = await request.json();\n        const { name, email, password } = body;\n        // Input validation\n        if (!name || !email || !password) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: \"Name, email and password are required\"\n            }, {\n                status: 400\n            });\n        }\n        // Basic email validation\n        const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n        if (!emailRegex.test(email)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: \"Please enter a valid email address\"\n            }, {\n                status: 400\n            });\n        }\n        // Password strength validation\n        if (password.length < 8) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: \"Password must be at least 8 characters long\"\n            }, {\n                status: 400\n            });\n        }\n        // Forward request to backend API\n        const backendResponse = await fetch(`${\"http://localhost:5000\"}/api/auth/register`, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                name,\n                email,\n                password\n            })\n        });\n        const backendData = await backendResponse.json();\n        if (backendResponse.ok && backendData.success) {\n            // Set httpOnly cookie with the JWT token\n            (0,_lib_server_auth__WEBPACK_IMPORTED_MODULE_1__.setAuthCookie)(backendData.data.token, 30 * 24 * 60 * 60); // 30 days\n            // Return user data without the token\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                message: backendData.message,\n                user: backendData.data.user\n            });\n        } else {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: backendData.message || \"Registration failed\"\n            }, {\n                status: backendResponse.status\n            });\n        }\n    } catch (error) {\n        console.error(\"Registration API error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            message: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n// Handle preflight requests\nasync function OPTIONS() {\n    return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(null, {\n        status: 200,\n        headers: {\n            \"Access-Control-Allow-Origin\": \"http://localhost:3000\" || 0,\n            \"Access-Control-Allow-Methods\": \"POST, OPTIONS\",\n            \"Access-Control-Allow-Headers\": \"Content-Type, X-CSRF-Token\",\n            \"Access-Control-Allow-Credentials\": \"true\"\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/auth/register/route.ts\n");

/***/ }),

/***/ "(rsc)/./app/api/csrf-token/route.ts":
/*!*************************************!*\
  !*** ./app/api/csrf-token/route.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   csrfValidator: () => (/* binding */ validateCSRFToken),\n/* harmony export */   validateCSRFToken: () => (/* binding */ validateCSRFToken)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_2__);\n/**\n * CSRF token generation endpoint\n */ \n\n\n// Store CSRF tokens temporarily (in production, use Redis or database)\nconst csrfTokens = new Map();\n/**\n * Generate a secure CSRF token\n */ function generateCSRFToken() {\n    return crypto__WEBPACK_IMPORTED_MODULE_2___default().randomBytes(32).toString(\"hex\");\n}\n/**\n * Get or create CSRF token for the session\n */ async function GET(request) {\n    try {\n        const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n        // Get session identifier (could be from auth token or create a session ID)\n        let sessionId = cookieStore.get(\"session-id\")?.value;\n        if (!sessionId) {\n            // Create a new session ID\n            sessionId = crypto__WEBPACK_IMPORTED_MODULE_2___default().randomBytes(16).toString(\"hex\");\n            cookieStore.set(\"session-id\", sessionId, {\n                httpOnly: true,\n                secure: \"development\" === \"production\",\n                sameSite: \"strict\",\n                maxAge: 24 * 60 * 60,\n                path: \"/\"\n            });\n        }\n        // Check if we have a valid CSRF token for this session\n        const existingToken = csrfTokens.get(sessionId);\n        const now = Date.now();\n        if (existingToken && existingToken.expires > now) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                token: existingToken.token\n            });\n        }\n        // Generate new CSRF token\n        const newToken = generateCSRFToken();\n        const expires = now + 60 * 60 * 1000; // 1 hour\n        csrfTokens.set(sessionId, {\n            token: newToken,\n            expires\n        });\n        // Clean up expired tokens\n        for (const [key, value] of csrfTokens.entries()){\n            if (value.expires <= now) {\n                csrfTokens.delete(key);\n            }\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            token: newToken\n        });\n    } catch (error) {\n        console.error(\"CSRF token generation error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            message: \"Failed to generate CSRF token\"\n        }, {\n            status: 500\n        });\n    }\n}\n/**\n * Validate CSRF token\n */ function validateCSRFToken(token, sessionId) {\n    // In development, allow a simple bypass for testing\n    if ( true && token === \"dev-bypass\") {\n        return true;\n    }\n    const storedToken = csrfTokens.get(sessionId);\n    if (!storedToken) {\n        return false;\n    }\n    if (storedToken.expires <= Date.now()) {\n        csrfTokens.delete(sessionId);\n        return false;\n    }\n    return storedToken.token === token;\n}\n// Export the validation function for use in other API routes\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/csrf-token/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/server-auth.ts":
/*!****************************!*\
  !*** ./lib/server-auth.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearAuthCookie: () => (/* binding */ clearAuthCookie),\n/* harmony export */   getAuthCookie: () => (/* binding */ getAuthCookie),\n/* harmony export */   getAuthenticatedUser: () => (/* binding */ getAuthenticatedUser),\n/* harmony export */   setAuthCookie: () => (/* binding */ setAuthCookie),\n/* harmony export */   verifyAuthToken: () => (/* binding */ verifyAuthToken)\n/* harmony export */ });\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/**\n * Server-side authentication utilities using httpOnly cookies\n * This file should only be imported in server components and API routes\n */ \n/**\n * Set authentication cookie (server-side only)\n */ function setAuthCookie(token, maxAge = 30 * 24 * 60 * 60) {\n    const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_0__.cookies)();\n    cookieStore.set(\"auth-token\", token, {\n        httpOnly: true,\n        secure: \"development\" === \"production\",\n        sameSite: \"strict\",\n        maxAge,\n        path: \"/\"\n    });\n}\n/**\n * Get authentication token from cookie (server-side only)\n */ function getAuthCookie() {\n    try {\n        const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_0__.cookies)();\n        const token = cookieStore.get(\"auth-token\");\n        return token?.value || null;\n    } catch (error) {\n        console.error(\"Error getting auth cookie:\", error);\n        return null;\n    }\n}\n/**\n * Clear authentication cookie (server-side only)\n */ function clearAuthCookie() {\n    const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_0__.cookies)();\n    cookieStore.delete(\"auth-token\");\n}\n/**\n * Verify authentication token with backend (server-side only)\n */ async function verifyAuthToken(token) {\n    try {\n        const response = await fetch(`${\"http://localhost:5000\"}/api/auth/me`, {\n            method: \"GET\",\n            headers: {\n                \"Authorization\": `Bearer ${token}`,\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        if (response.ok) {\n            const userData = await response.json();\n            return {\n                isValid: true,\n                user: userData.user\n            };\n        } else {\n            return {\n                isValid: false,\n                error: \"Invalid or expired token\"\n            };\n        }\n    } catch (error) {\n        console.error(\"Token verification error:\", error);\n        return {\n            isValid: false,\n            error: \"Token verification failed\"\n        };\n    }\n}\n/**\n * Get authenticated user from cookie (server-side only)\n */ async function getAuthenticatedUser() {\n    const token = getAuthCookie();\n    if (!token) {\n        return {\n            isAuthenticated: false,\n            error: \"No authentication token found\"\n        };\n    }\n    const verification = await verifyAuthToken(token);\n    if (verification.isValid) {\n        return {\n            isAuthenticated: true,\n            user: verification.user\n        };\n    } else {\n        // Clear invalid token\n        clearAuthCookie();\n        return {\n            isAuthenticated: false,\n            error: verification.error\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvc2VydmVyLWF1dGgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQUE7OztDQUdDLEdBRXNDO0FBRXZDOztDQUVDLEdBQ00sU0FBU0MsY0FBY0MsS0FBYSxFQUFFQyxTQUFpQixLQUFLLEtBQUssS0FBSyxFQUFFO0lBQzdFLE1BQU1DLGNBQWNKLHFEQUFPQTtJQUMzQkksWUFBWUMsR0FBRyxDQUFDLGNBQWNILE9BQU87UUFDbkNJLFVBQVU7UUFDVkMsUUFBUUMsa0JBQXlCO1FBQ2pDQyxVQUFVO1FBQ1ZOO1FBQ0FPLE1BQU07SUFDUjtBQUNGO0FBRUE7O0NBRUMsR0FDTSxTQUFTQztJQUNkLElBQUk7UUFDRixNQUFNUCxjQUFjSixxREFBT0E7UUFDM0IsTUFBTUUsUUFBUUUsWUFBWVEsR0FBRyxDQUFDO1FBQzlCLE9BQU9WLE9BQU9XLFNBQVM7SUFDekIsRUFBRSxPQUFPQyxPQUFPO1FBQ2RDLFFBQVFELEtBQUssQ0FBQyw4QkFBOEJBO1FBQzVDLE9BQU87SUFDVDtBQUNGO0FBRUE7O0NBRUMsR0FDTSxTQUFTRTtJQUNkLE1BQU1aLGNBQWNKLHFEQUFPQTtJQUMzQkksWUFBWWEsTUFBTSxDQUFDO0FBQ3JCO0FBRUE7O0NBRUMsR0FDTSxlQUFlQyxnQkFBZ0JoQixLQUFhO0lBS2pELElBQUk7UUFDRixNQUFNaUIsV0FBVyxNQUFNQyxNQUFNLENBQUMsRUFBRVosdUJBQStCLENBQUMsWUFBWSxDQUFDLEVBQUU7WUFDN0VlLFFBQVE7WUFDUkMsU0FBUztnQkFDUCxpQkFBaUIsQ0FBQyxPQUFPLEVBQUV0QixNQUFNLENBQUM7Z0JBQ2xDLGdCQUFnQjtZQUNsQjtRQUNGO1FBRUEsSUFBSWlCLFNBQVNNLEVBQUUsRUFBRTtZQUNmLE1BQU1DLFdBQVcsTUFBTVAsU0FBU1EsSUFBSTtZQUNwQyxPQUFPO2dCQUNMQyxTQUFTO2dCQUNUQyxNQUFNSCxTQUFTRyxJQUFJO1lBQ3JCO1FBQ0YsT0FBTztZQUNMLE9BQU87Z0JBQ0xELFNBQVM7Z0JBQ1RkLE9BQU87WUFDVDtRQUNGO0lBQ0YsRUFBRSxPQUFPQSxPQUFPO1FBQ2RDLFFBQVFELEtBQUssQ0FBQyw2QkFBNkJBO1FBQzNDLE9BQU87WUFDTGMsU0FBUztZQUNUZCxPQUFPO1FBQ1Q7SUFDRjtBQUNGO0FBRUE7O0NBRUMsR0FDTSxlQUFlZ0I7SUFLcEIsTUFBTTVCLFFBQVFTO0lBRWQsSUFBSSxDQUFDVCxPQUFPO1FBQ1YsT0FBTztZQUNMNkIsaUJBQWlCO1lBQ2pCakIsT0FBTztRQUNUO0lBQ0Y7SUFFQSxNQUFNa0IsZUFBZSxNQUFNZCxnQkFBZ0JoQjtJQUUzQyxJQUFJOEIsYUFBYUosT0FBTyxFQUFFO1FBQ3hCLE9BQU87WUFDTEcsaUJBQWlCO1lBQ2pCRixNQUFNRyxhQUFhSCxJQUFJO1FBQ3pCO0lBQ0YsT0FBTztRQUNMLHNCQUFzQjtRQUN0QmI7UUFDQSxPQUFPO1lBQ0xlLGlCQUFpQjtZQUNqQmpCLE9BQU9rQixhQUFhbEIsS0FBSztRQUMzQjtJQUNGO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9teS12MC1wcm9qZWN0Ly4vbGliL3NlcnZlci1hdXRoLnRzPzRhOTMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBTZXJ2ZXItc2lkZSBhdXRoZW50aWNhdGlvbiB1dGlsaXRpZXMgdXNpbmcgaHR0cE9ubHkgY29va2llc1xuICogVGhpcyBmaWxlIHNob3VsZCBvbmx5IGJlIGltcG9ydGVkIGluIHNlcnZlciBjb21wb25lbnRzIGFuZCBBUEkgcm91dGVzXG4gKi9cblxuaW1wb3J0IHsgY29va2llcyB9IGZyb20gJ25leHQvaGVhZGVycyc7XG5cbi8qKlxuICogU2V0IGF1dGhlbnRpY2F0aW9uIGNvb2tpZSAoc2VydmVyLXNpZGUgb25seSlcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHNldEF1dGhDb29raWUodG9rZW46IHN0cmluZywgbWF4QWdlOiBudW1iZXIgPSAzMCAqIDI0ICogNjAgKiA2MCkge1xuICBjb25zdCBjb29raWVTdG9yZSA9IGNvb2tpZXMoKTtcbiAgY29va2llU3RvcmUuc2V0KCdhdXRoLXRva2VuJywgdG9rZW4sIHtcbiAgICBodHRwT25seTogdHJ1ZSxcbiAgICBzZWN1cmU6IHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAncHJvZHVjdGlvbicsXG4gICAgc2FtZVNpdGU6ICdzdHJpY3QnLFxuICAgIG1heEFnZSxcbiAgICBwYXRoOiAnLycsXG4gIH0pO1xufVxuXG4vKipcbiAqIEdldCBhdXRoZW50aWNhdGlvbiB0b2tlbiBmcm9tIGNvb2tpZSAoc2VydmVyLXNpZGUgb25seSlcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGdldEF1dGhDb29raWUoKTogc3RyaW5nIHwgbnVsbCB7XG4gIHRyeSB7XG4gICAgY29uc3QgY29va2llU3RvcmUgPSBjb29raWVzKCk7XG4gICAgY29uc3QgdG9rZW4gPSBjb29raWVTdG9yZS5nZXQoJ2F1dGgtdG9rZW4nKTtcbiAgICByZXR1cm4gdG9rZW4/LnZhbHVlIHx8IG51bGw7XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgZ2V0dGluZyBhdXRoIGNvb2tpZTonLCBlcnJvcik7XG4gICAgcmV0dXJuIG51bGw7XG4gIH1cbn1cblxuLyoqXG4gKiBDbGVhciBhdXRoZW50aWNhdGlvbiBjb29raWUgKHNlcnZlci1zaWRlIG9ubHkpXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBjbGVhckF1dGhDb29raWUoKSB7XG4gIGNvbnN0IGNvb2tpZVN0b3JlID0gY29va2llcygpO1xuICBjb29raWVTdG9yZS5kZWxldGUoJ2F1dGgtdG9rZW4nKTtcbn1cblxuLyoqXG4gKiBWZXJpZnkgYXV0aGVudGljYXRpb24gdG9rZW4gd2l0aCBiYWNrZW5kIChzZXJ2ZXItc2lkZSBvbmx5KVxuICovXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gdmVyaWZ5QXV0aFRva2VuKHRva2VuOiBzdHJpbmcpOiBQcm9taXNlPHtcbiAgaXNWYWxpZDogYm9vbGVhbjtcbiAgdXNlcj86IGFueTtcbiAgZXJyb3I/OiBzdHJpbmc7XG59PiB7XG4gIHRyeSB7XG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHtwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19BUElfVVJMfS9hcGkvYXV0aC9tZWAsIHtcbiAgICAgIG1ldGhvZDogJ0dFVCcsXG4gICAgICBoZWFkZXJzOiB7XG4gICAgICAgICdBdXRob3JpemF0aW9uJzogYEJlYXJlciAke3Rva2VufWAsXG4gICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICB9LFxuICAgIH0pO1xuXG4gICAgaWYgKHJlc3BvbnNlLm9rKSB7XG4gICAgICBjb25zdCB1c2VyRGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgIHJldHVybiB7XG4gICAgICAgIGlzVmFsaWQ6IHRydWUsXG4gICAgICAgIHVzZXI6IHVzZXJEYXRhLnVzZXIsXG4gICAgICB9O1xuICAgIH0gZWxzZSB7XG4gICAgICByZXR1cm4ge1xuICAgICAgICBpc1ZhbGlkOiBmYWxzZSxcbiAgICAgICAgZXJyb3I6ICdJbnZhbGlkIG9yIGV4cGlyZWQgdG9rZW4nLFxuICAgICAgfTtcbiAgICB9XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignVG9rZW4gdmVyaWZpY2F0aW9uIGVycm9yOicsIGVycm9yKTtcbiAgICByZXR1cm4ge1xuICAgICAgaXNWYWxpZDogZmFsc2UsXG4gICAgICBlcnJvcjogJ1Rva2VuIHZlcmlmaWNhdGlvbiBmYWlsZWQnLFxuICAgIH07XG4gIH1cbn1cblxuLyoqXG4gKiBHZXQgYXV0aGVudGljYXRlZCB1c2VyIGZyb20gY29va2llIChzZXJ2ZXItc2lkZSBvbmx5KVxuICovXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZ2V0QXV0aGVudGljYXRlZFVzZXIoKTogUHJvbWlzZTx7XG4gIGlzQXV0aGVudGljYXRlZDogYm9vbGVhbjtcbiAgdXNlcj86IGFueTtcbiAgZXJyb3I/OiBzdHJpbmc7XG59PiB7XG4gIGNvbnN0IHRva2VuID0gZ2V0QXV0aENvb2tpZSgpO1xuICBcbiAgaWYgKCF0b2tlbikge1xuICAgIHJldHVybiB7XG4gICAgICBpc0F1dGhlbnRpY2F0ZWQ6IGZhbHNlLFxuICAgICAgZXJyb3I6ICdObyBhdXRoZW50aWNhdGlvbiB0b2tlbiBmb3VuZCcsXG4gICAgfTtcbiAgfVxuXG4gIGNvbnN0IHZlcmlmaWNhdGlvbiA9IGF3YWl0IHZlcmlmeUF1dGhUb2tlbih0b2tlbik7XG4gIFxuICBpZiAodmVyaWZpY2F0aW9uLmlzVmFsaWQpIHtcbiAgICByZXR1cm4ge1xuICAgICAgaXNBdXRoZW50aWNhdGVkOiB0cnVlLFxuICAgICAgdXNlcjogdmVyaWZpY2F0aW9uLnVzZXIsXG4gICAgfTtcbiAgfSBlbHNlIHtcbiAgICAvLyBDbGVhciBpbnZhbGlkIHRva2VuXG4gICAgY2xlYXJBdXRoQ29va2llKCk7XG4gICAgcmV0dXJuIHtcbiAgICAgIGlzQXV0aGVudGljYXRlZDogZmFsc2UsXG4gICAgICBlcnJvcjogdmVyaWZpY2F0aW9uLmVycm9yLFxuICAgIH07XG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJjb29raWVzIiwic2V0QXV0aENvb2tpZSIsInRva2VuIiwibWF4QWdlIiwiY29va2llU3RvcmUiLCJzZXQiLCJodHRwT25seSIsInNlY3VyZSIsInByb2Nlc3MiLCJzYW1lU2l0ZSIsInBhdGgiLCJnZXRBdXRoQ29va2llIiwiZ2V0IiwidmFsdWUiLCJlcnJvciIsImNvbnNvbGUiLCJjbGVhckF1dGhDb29raWUiLCJkZWxldGUiLCJ2ZXJpZnlBdXRoVG9rZW4iLCJyZXNwb25zZSIsImZldGNoIiwiZW52IiwiTkVYVF9QVUJMSUNfQVBJX1VSTCIsIm1ldGhvZCIsImhlYWRlcnMiLCJvayIsInVzZXJEYXRhIiwianNvbiIsImlzVmFsaWQiLCJ1c2VyIiwiZ2V0QXV0aGVudGljYXRlZFVzZXIiLCJpc0F1dGhlbnRpY2F0ZWQiLCJ2ZXJpZmljYXRpb24iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./lib/server-auth.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Fregister%2Froute&page=%2Fapi%2Fauth%2Fregister%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fregister%2Froute.ts&appDir=D%3A%5CDev%20Ideas%5CCareerPilotAI-2%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CDev%20Ideas%5CCareerPilotAI-2%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();