/**
 * Security middleware for production-ready security measures
 */
const logger = require("../utils/logger");

/**
 * Rate limiting store (in-memory for simplicity, use Redis in production)
 */
const rateLimitStore = new Map();

/**
 * Clean up old rate limit entries
 */
setInterval(() => {
  const now = Date.now();
  for (const [key, data] of rateLimitStore.entries()) {
    if (now - data.resetTime > 60000) {
      // 1 minute
      rateLimitStore.delete(key);
    }
  }
}, 60000); // Clean every minute

/**
 * Rate limiting middleware
 * @param {Object} options - Rate limiting options
 * @param {number} options.windowMs - Time window in milliseconds
 * @param {number} options.max - Maximum number of requests per window
 * @param {string} options.message - Error message when limit exceeded
 */
exports.rateLimit = (options = {}) => {
  const {
    windowMs = 15 * 60 * 1000, // 15 minutes
    max = 100, // 100 requests per window
    message = "Too many requests, please try again later",
  } = options;

  return (req, res, next) => {
    const key = req.ip || req.connection.remoteAddress;
    const now = Date.now();

    if (!rateLimitStore.has(key)) {
      rateLimitStore.set(key, {
        count: 1,
        resetTime: now + windowMs,
      });
      return next();
    }

    const data = rateLimitStore.get(key);

    if (now > data.resetTime) {
      // Reset the counter
      data.count = 1;
      data.resetTime = now + windowMs;
      return next();
    }

    if (data.count >= max) {
      logger.warn("Rate limit exceeded", {
        ip: key,
        url: req.originalUrl,
        userAgent: req.get("User-Agent"),
      });

      return res.status(429).json({
        success: false,
        message,
        retryAfter: Math.ceil((data.resetTime - now) / 1000),
      });
    }

    data.count++;
    next();
  };
};

/**
 * Security headers middleware
 */
exports.securityHeaders = (req, res, next) => {
  // Prevent clickjacking
  res.setHeader("X-Frame-Options", "DENY");

  // Prevent MIME type sniffing
  res.setHeader("X-Content-Type-Options", "nosniff");

  // Enable XSS protection
  res.setHeader("X-XSS-Protection", "1; mode=block");

  // Strict transport security (HTTPS only)
  if (process.env.NODE_ENV === "production") {
    res.setHeader(
      "Strict-Transport-Security",
      "max-age=31536000; includeSubDomains"
    );
  }

  // Content Security Policy
  res.setHeader(
    "Content-Security-Policy",
    "default-src 'self'; " +
      "script-src 'self' 'unsafe-inline'; " +
      "style-src 'self' 'unsafe-inline'; " +
      "img-src 'self' data: https:; " +
      "font-src 'self' https:; " +
      "connect-src 'self' https:; " +
      "frame-ancestors 'none';"
  );

  // Referrer Policy
  res.setHeader("Referrer-Policy", "strict-origin-when-cross-origin");

  // Remove server information
  res.removeHeader("X-Powered-By");

  next();
};

/**
 * Request logging middleware
 */
exports.requestLogger = (req, res, next) => {
  const start = Date.now();

  res.on("finish", () => {
    const duration = Date.now() - start;
    logger.request(
      req.method,
      req.originalUrl,
      req.user?.id,
      res.statusCode,
      duration
    );
  });

  next();
};

/**
 * IP whitelist middleware (for admin endpoints)
 */
exports.ipWhitelist = (allowedIPs = []) => {
  return (req, res, next) => {
    const clientIP = req.ip || req.connection.remoteAddress;

    if (allowedIPs.length > 0 && !allowedIPs.includes(clientIP)) {
      logger.warn("Unauthorized IP access attempt", {
        ip: clientIP,
        url: req.originalUrl,
      });

      return res.status(403).json({
        success: false,
        message: "Access denied from this IP address",
      });
    }

    next();
  };
};

/**
 * Suspicious activity detection
 */
const suspiciousPatterns = [
  /script/i,
  /javascript/i,
  /vbscript/i,
  /onload/i,
  /onerror/i,
  /eval\(/i,
  /expression\(/i,
  /<iframe/i,
  /<object/i,
  /<embed/i,
  /union.*select/i,
  /drop.*table/i,
  /insert.*into/i,
  /delete.*from/i,
];

exports.detectSuspiciousActivity = (req, res, next) => {
  const checkString = (str) => {
    return suspiciousPatterns.some((pattern) => pattern.test(str));
  };

  const checkObject = (obj, path = "") => {
    for (const key in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, key)) {
        const value = obj[key];
        const currentPath = path ? `${path}.${key}` : key;

        if (typeof value === "string" && checkString(value)) {
          logger.warn("Suspicious activity detected", {
            ip: req.ip,
            url: req.originalUrl,
            field: currentPath,
            pattern: value.substring(0, 100),
            userAgent: req.get("User-Agent"),
          });

          return res.status(400).json({
            success: false,
            message: "Invalid input detected",
          });
        } else if (typeof value === "object" && value !== null) {
          const result = checkObject(value, currentPath);
          if (result) return result;
        }
      }
    }
    return null;
  };

  // Check URL parameters
  if (req.query && typeof req.query === "object") {
    const result = checkObject(req.query);
    if (result) return result;
  }

  // Check request body
  if (req.body && typeof req.body === "object") {
    const result = checkObject(req.body);
    if (result) return result;
  }

  next();
};

/**
 * CORS configuration for production
 */
exports.corsOptions = {
  origin: function (origin, callback) {
    const allowedOrigins = process.env.ALLOWED_ORIGINS
      ? process.env.ALLOWED_ORIGINS.split(",")
      : ["http://localhost:3000", "http://localhost:3001"];

    // Allow requests with no origin (mobile apps, etc.)
    if (!origin) return callback(null, true);

    if (allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      logger.warn("CORS blocked request", { origin, ip: origin });
      callback(new Error("Not allowed by CORS"));
    }
  },
  credentials: true,
  optionsSuccessStatus: 200,
  methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
  allowedHeaders: ["Content-Type", "Authorization", "X-Requested-With"],
};
