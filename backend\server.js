const express = require("express");
const dotenv = require("dotenv");
const cors = require("cors");
const cookieParser = require("cookie-parser");
const { connectDB, createIndexes } = require("./config/database");
const errorHandler = require("./middleware/errorHandler");
const logger = require("./utils/logger");
const security = require("./middleware/security");
const { sanitizeBody } = require("./middleware/validator");

// Load environment variables
dotenv.config();

// Connect to database and create indexes
const initializeDatabase = async () => {
  try {
    await connectDB();
    await createIndexes();
    logger.info("Database initialization completed");
  } catch (error) {
    logger.error("Database initialization failed", { error: error.message });
    process.exit(1);
  }
};

initializeDatabase();

// Initialize app
const app = express();

// Security middleware
app.use(security.securityHeaders);
app.use(security.requestLogger);
app.use(
  security.rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // 100 requests per window
    message: "Too many requests from this IP, please try again later",
  })
);

// CORS with security configuration
app.use(cors(security.corsOptions));

// Body parsing with size limit
app.use(express.json({ limit: "10mb" }));

// Cookie parser
app.use(cookieParser());

// Input sanitization
app.use(sanitizeBody);
app.use(security.detectSuspiciousActivity);

// Special handling for Razorpay webhook
app.use("/api/payment/webhook", express.raw({ type: "application/json" }));

// Routes
app.use("/api/health", require("./routes/healthRoutes"));
app.use("/api/auth", require("./routes/authRoutes"));
app.use("/api/generate/resume", require("./routes/resumeRoutes"));
app.use("/api/generate/cover-letter", require("./routes/coverLetterRoutes"));
app.use("/api/generate/linkedin", require("./routes/linkedinRoutes"));
app.use("/api/pdf", require("./routes/pdfRoutes"));
app.use("/api/export", require("./routes/exportRoutes"));
app.use("/api/users", require("./routes/userRoutes"));
app.use("/api/payment", require("./routes/paymentRoutes"));

// Home route
app.get("/", (req, res) => {
  res.json({
    success: true,
    message: "ResumAI API is running",
  });
});

// Error handling middleware
app.use(errorHandler);

// Start server
const PORT = process.env.PORT || 5000;
const server = app.listen(PORT, () => {
  logger.info(`Server started`, {
    port: PORT,
    environment: process.env.NODE_ENV || "development",
  });
});

// Handle unhandled promise rejections
process.on("unhandledRejection", (err, promise) => {
  logger.error("Unhandled Promise Rejection", {
    error: err.message,
    stack: err.stack,
  });
  // Close server & exit process
  server.close(() => process.exit(1));
});
