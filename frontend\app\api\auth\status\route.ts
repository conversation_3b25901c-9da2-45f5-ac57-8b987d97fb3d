/**
 * Authentication status check API route
 */
import { NextRequest, NextResponse } from 'next/server';
import { getAuthenticatedUser, clearAuthCookie } from '@/lib/server-auth';

export async function GET(request: NextRequest) {
  try {
    const authResult = await getAuthenticatedUser();

    if (authResult.isAuthenticated) {
      return NextResponse.json({
        success: true,
        user: authResult.user,
        isAuthenticated: true,
      });
    } else {
      return NextResponse.json(
        { success: false, message: authResult.error || 'Not authenticated' },
        { status: 401 }
      );
    }
  } catch (error) {
    console.error('Auth status check error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Handle preflight requests
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': process.env.NEXT_PUBLIC_FRONTEND_URL || 'http://localhost:3000',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
      'Access-Control-Allow-Credentials': 'true',
    },
  });
}
