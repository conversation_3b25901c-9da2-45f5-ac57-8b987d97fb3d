/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/(auth)/login/page";
exports.ids = ["app/(auth)/login/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F(auth)%2Flogin%2Fpage&page=%2F(auth)%2Flogin%2Fpage&appPaths=%2F(auth)%2Flogin%2Fpage&pagePath=private-next-app-dir%2F(auth)%2Flogin%2Fpage.tsx&appDir=D%3A%5CDev%20Ideas%5CCareerPilotAI-2%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CDev%20Ideas%5CCareerPilotAI-2%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F(auth)%2Flogin%2Fpage&page=%2F(auth)%2Flogin%2Fpage&appPaths=%2F(auth)%2Flogin%2Fpage&pagePath=private-next-app-dir%2F(auth)%2Flogin%2Fpage.tsx&appDir=D%3A%5CDev%20Ideas%5CCareerPilotAI-2%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CDev%20Ideas%5CCareerPilotAI-2%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9100\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '(auth)',\n        {\n        children: [\n        'login',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/(auth)/login/page.tsx */ \"(rsc)/./app/(auth)/login/page.tsx\")), \"D:\\\\Dev Ideas\\\\CareerPilotAI-2\\\\frontend\\\\app\\\\(auth)\\\\login\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"D:\\\\Dev Ideas\\\\CareerPilotAI-2\\\\frontend\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/not-found.tsx */ \"(rsc)/./app/not-found.tsx\")), \"D:\\\\Dev Ideas\\\\CareerPilotAI-2\\\\frontend\\\\app\\\\not-found.tsx\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\Dev Ideas\\\\CareerPilotAI-2\\\\frontend\\\\app\\\\(auth)\\\\login\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/(auth)/login/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/(auth)/login/page\",\n        pathname: \"/login\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkYoYXV0aCklMkZsb2dpbiUyRnBhZ2UmcGFnZT0lMkYoYXV0aCklMkZsb2dpbiUyRnBhZ2UmYXBwUGF0aHM9JTJGKGF1dGgpJTJGbG9naW4lMkZwYWdlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGKGF1dGgpJTJGbG9naW4lMkZwYWdlLnRzeCZhcHBEaXI9RCUzQSU1Q0RldiUyMElkZWFzJTVDQ2FyZWVyUGlsb3RBSS0yJTVDZnJvbnRlbmQlNUNhcHAmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZyb290RGlyPUQlM0ElNUNEZXYlMjBJZGVhcyU1Q0NhcmVlclBpbG90QUktMiU1Q2Zyb250ZW5kJmlzRGV2PXRydWUmdHNjb25maWdQYXRoPXRzY29uZmlnLmpzb24mYmFzZVBhdGg9JmFzc2V0UHJlZml4PSZuZXh0Q29uZmlnT3V0cHV0PSZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEsYUFBYSxzQkFBc0I7QUFDaUU7QUFDckM7QUFDL0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDO0FBQ2pDLHVCQUF1QixrS0FBMkc7QUFDbEk7QUFDQSxTQUFTO0FBQ1QsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBLHlCQUF5Qiw0SUFBOEY7QUFDdkgsb0JBQW9CLGtKQUFpRztBQUNySDtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDdUI7QUFDNkQ7QUFDcEYsNkJBQTZCLG1CQUFtQjtBQUNoRDtBQUNPO0FBQ0E7QUFDUDtBQUNBO0FBQ0E7QUFDdUQ7QUFDdkQ7QUFDTyx3QkFBd0IsOEdBQWtCO0FBQ2pEO0FBQ0EsY0FBYyx5RUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsQ0FBQzs7QUFFRCIsInNvdXJjZXMiOlsid2VicGFjazovL215LXYwLXByb2plY3QvP2I2YmYiXSwic291cmNlc0NvbnRlbnQiOlsiXCJUVVJCT1BBQ0sgeyB0cmFuc2l0aW9uOiBuZXh0LXNzciB9XCI7XG5pbXBvcnQgeyBBcHBQYWdlUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUtbW9kdWxlcy9hcHAtcGFnZS9tb2R1bGUuY29tcGlsZWRcIjtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1raW5kXCI7XG4vLyBXZSBpbmplY3QgdGhlIHRyZWUgYW5kIHBhZ2VzIGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCB0cmVlID0ge1xuICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAnJyxcbiAgICAgICAge1xuICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAnKGF1dGgpJyxcbiAgICAgICAge1xuICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAnbG9naW4nLFxuICAgICAgICB7XG4gICAgICAgIGNoaWxkcmVuOiBbJ19fUEFHRV9fJywge30sIHtcbiAgICAgICAgICBwYWdlOiBbKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxEZXYgSWRlYXNcXFxcQ2FyZWVyUGlsb3RBSS0yXFxcXGZyb250ZW5kXFxcXGFwcFxcXFwoYXV0aClcXFxcbG9naW5cXFxccGFnZS50c3hcIiksIFwiRDpcXFxcRGV2IElkZWFzXFxcXENhcmVlclBpbG90QUktMlxcXFxmcm9udGVuZFxcXFxhcHBcXFxcKGF1dGgpXFxcXGxvZ2luXFxcXHBhZ2UudHN4XCJdLFxuICAgICAgICAgIFxuICAgICAgICB9XVxuICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICBcbiAgICAgICAgXG4gICAgICB9XG4gICAgICBdXG4gICAgICB9LFxuICAgICAgICB7XG4gICAgICAgIFxuICAgICAgICBcbiAgICAgIH1cbiAgICAgIF1cbiAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgJ2xheW91dCc6IFsoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXERldiBJZGVhc1xcXFxDYXJlZXJQaWxvdEFJLTJcXFxcZnJvbnRlbmRcXFxcYXBwXFxcXGxheW91dC50c3hcIiksIFwiRDpcXFxcRGV2IElkZWFzXFxcXENhcmVlclBpbG90QUktMlxcXFxmcm9udGVuZFxcXFxhcHBcXFxcbGF5b3V0LnRzeFwiXSxcbidub3QtZm91bmQnOiBbKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxEZXYgSWRlYXNcXFxcQ2FyZWVyUGlsb3RBSS0yXFxcXGZyb250ZW5kXFxcXGFwcFxcXFxub3QtZm91bmQudHN4XCIpLCBcIkQ6XFxcXERldiBJZGVhc1xcXFxDYXJlZXJQaWxvdEFJLTJcXFxcZnJvbnRlbmRcXFxcYXBwXFxcXG5vdC1mb3VuZC50c3hcIl0sXG4gICAgICAgIFxuICAgICAgfVxuICAgICAgXVxuICAgICAgfS5jaGlsZHJlbjtcbmNvbnN0IHBhZ2VzID0gW1wiRDpcXFxcRGV2IElkZWFzXFxcXENhcmVlclBpbG90QUktMlxcXFxmcm9udGVuZFxcXFxhcHBcXFxcKGF1dGgpXFxcXGxvZ2luXFxcXHBhZ2UudHN4XCJdO1xuZXhwb3J0IHsgdHJlZSwgcGFnZXMgfTtcbmV4cG9ydCB7IGRlZmF1bHQgYXMgR2xvYmFsRXJyb3IgfSBmcm9tIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2Vycm9yLWJvdW5kYXJ5XCI7XG5jb25zdCBfX25leHRfYXBwX3JlcXVpcmVfXyA9IF9fd2VicGFja19yZXF1aXJlX19cbmNvbnN0IF9fbmV4dF9hcHBfbG9hZF9jaHVua19fID0gKCkgPT4gUHJvbWlzZS5yZXNvbHZlKClcbmV4cG9ydCBjb25zdCBvcmlnaW5hbFBhdGhuYW1lID0gXCIvKGF1dGgpL2xvZ2luL3BhZ2VcIjtcbmV4cG9ydCBjb25zdCBfX25leHRfYXBwX18gPSB7XG4gICAgcmVxdWlyZTogX19uZXh0X2FwcF9yZXF1aXJlX18sXG4gICAgbG9hZENodW5rOiBfX25leHRfYXBwX2xvYWRfY2h1bmtfX1xufTtcbmV4cG9ydCAqIGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2FwcC1yZW5kZXIvZW50cnktYmFzZVwiO1xuLy8gQ3JlYXRlIGFuZCBleHBvcnQgdGhlIHJvdXRlIG1vZHVsZSB0aGF0IHdpbGwgYmUgY29uc3VtZWQuXG5leHBvcnQgY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgQXBwUGFnZVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5BUFBfUEFHRSxcbiAgICAgICAgcGFnZTogXCIvKGF1dGgpL2xvZ2luL3BhZ2VcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL2xvZ2luXCIsXG4gICAgICAgIC8vIFRoZSBmb2xsb3dpbmcgYXJlbid0IHVzZWQgaW4gcHJvZHVjdGlvbi5cbiAgICAgICAgYnVuZGxlUGF0aDogXCJcIixcbiAgICAgICAgZmlsZW5hbWU6IFwiXCIsXG4gICAgICAgIGFwcFBhdGhzOiBbXVxuICAgIH0sXG4gICAgdXNlcmxhbmQ6IHtcbiAgICAgICAgbG9hZGVyVHJlZTogdHJlZVxuICAgIH1cbn0pO1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcHAtcGFnZS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F(auth)%2Flogin%2Fpage&page=%2F(auth)%2Flogin%2Fpage&appPaths=%2F(auth)%2Flogin%2Fpage&pagePath=private-next-app-dir%2F(auth)%2Flogin%2Fpage.tsx&appDir=D%3A%5CDev%20Ideas%5CCareerPilotAI-2%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CDev%20Ideas%5CCareerPilotAI-2%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDev%20Ideas%5C%5CCareerPilotAI-2%5C%5Cfrontend%5C%5Capp%5C%5C(auth)%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDev%20Ideas%5C%5CCareerPilotAI-2%5C%5Cfrontend%5C%5Capp%5C%5C(auth)%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/(auth)/login/page.tsx */ \"(ssr)/./app/(auth)/login/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNEZXYlMjBJZGVhcyU1QyU1Q0NhcmVlclBpbG90QUktMiU1QyU1Q2Zyb250ZW5kJTVDJTVDYXBwJTVDJTVDKGF1dGgpJTVDJTVDbG9naW4lNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa0tBQTJHIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbXktdjAtcHJvamVjdC8/Y2Y0NiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXERldiBJZGVhc1xcXFxDYXJlZXJQaWxvdEFJLTJcXFxcZnJvbnRlbmRcXFxcYXBwXFxcXChhdXRoKVxcXFxsb2dpblxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDev%20Ideas%5C%5CCareerPilotAI-2%5C%5Cfrontend%5C%5Capp%5C%5C(auth)%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDev%20Ideas%5C%5CCareerPilotAI-2%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDev%20Ideas%5C%5CCareerPilotAI-2%5C%5Cfrontend%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDev%20Ideas%5C%5CCareerPilotAI-2%5C%5Cfrontend%5C%5Ccomponents%5C%5Cui%5C%5Ctoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDev%20Ideas%5C%5CCareerPilotAI-2%5C%5Cfrontend%5C%5Clib%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDev%20Ideas%5C%5CCareerPilotAI-2%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDev%20Ideas%5C%5CCareerPilotAI-2%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDev%20Ideas%5C%5CCareerPilotAI-2%5C%5Cfrontend%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDev%20Ideas%5C%5CCareerPilotAI-2%5C%5Cfrontend%5C%5Ccomponents%5C%5Cui%5C%5Ctoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDev%20Ideas%5C%5CCareerPilotAI-2%5C%5Cfrontend%5C%5Clib%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDev%20Ideas%5C%5CCareerPilotAI-2%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/theme-provider.tsx */ \"(ssr)/./components/theme-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ui/toaster.tsx */ \"(ssr)/./components/ui/toaster.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./lib/auth-context.tsx */ \"(ssr)/./lib/auth-context.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDev%20Ideas%5C%5CCareerPilotAI-2%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDev%20Ideas%5C%5CCareerPilotAI-2%5C%5Cfrontend%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDev%20Ideas%5C%5CCareerPilotAI-2%5C%5Cfrontend%5C%5Ccomponents%5C%5Cui%5C%5Ctoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDev%20Ideas%5C%5CCareerPilotAI-2%5C%5Cfrontend%5C%5Clib%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDev%20Ideas%5C%5CCareerPilotAI-2%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDev%20Ideas%5C%5CCareerPilotAI-2%5C%5Cfrontend%5C%5Capp%5C%5Cnot-found.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDev%20Ideas%5C%5CCareerPilotAI-2%5C%5Cfrontend%5C%5Capp%5C%5Cnot-found.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/not-found.tsx */ \"(ssr)/./app/not-found.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNEZXYlMjBJZGVhcyU1QyU1Q0NhcmVlclBpbG90QUktMiU1QyU1Q2Zyb250ZW5kJTVDJTVDYXBwJTVDJTVDbm90LWZvdW5kLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa0pBQWlHIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbXktdjAtcHJvamVjdC8/OGUxNSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXERldiBJZGVhc1xcXFxDYXJlZXJQaWxvdEFJLTJcXFxcZnJvbnRlbmRcXFxcYXBwXFxcXG5vdC1mb3VuZC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDev%20Ideas%5C%5CCareerPilotAI-2%5C%5Cfrontend%5C%5Capp%5C%5Cnot-found.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDev%20Ideas%5C%5CCareerPilotAI-2%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDev%20Ideas%5C%5CCareerPilotAI-2%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDev%20Ideas%5C%5CCareerPilotAI-2%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDev%20Ideas%5C%5CCareerPilotAI-2%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDev%20Ideas%5C%5CCareerPilotAI-2%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDev%20Ideas%5C%5CCareerPilotAI-2%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDev%20Ideas%5C%5CCareerPilotAI-2%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDev%20Ideas%5C%5CCareerPilotAI-2%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDev%20Ideas%5C%5CCareerPilotAI-2%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDev%20Ideas%5C%5CCareerPilotAI-2%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDev%20Ideas%5C%5CCareerPilotAI-2%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDev%20Ideas%5C%5CCareerPilotAI-2%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNEZXYlMjBJZGVhcyU1QyU1Q0NhcmVlclBpbG90QUktMiU1QyU1Q2Zyb250ZW5kJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDYXBwLXJvdXRlci5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDRGV2JTIwSWRlYXMlNUMlNUNDYXJlZXJQaWxvdEFJLTIlNUMlNUNmcm9udGVuZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2NsaWVudC1wYWdlLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNEZXYlMjBJZGVhcyU1QyU1Q0NhcmVlclBpbG90QUktMiU1QyU1Q2Zyb250ZW5kJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDZXJyb3ItYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q0RldiUyMElkZWFzJTVDJTVDQ2FyZWVyUGlsb3RBSS0yJTVDJTVDZnJvbnRlbmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNsYXlvdXQtcm91dGVyLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNEZXYlMjBJZGVhcyU1QyU1Q0NhcmVlclBpbG90QUktMiU1QyU1Q2Zyb250ZW5kJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDbm90LWZvdW5kLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNEZXYlMjBJZGVhcyU1QyU1Q0NhcmVlclBpbG90QUktMiU1QyU1Q2Zyb250ZW5kJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDcmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa09BQTBJO0FBQzFJO0FBQ0Esb09BQTJJO0FBQzNJO0FBQ0EsME9BQThJO0FBQzlJO0FBQ0Esd09BQTZJO0FBQzdJO0FBQ0Esa1BBQWtKO0FBQ2xKO0FBQ0Esc1FBQTRKIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbXktdjAtcHJvamVjdC8/OWE3NCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXERldiBJZGVhc1xcXFxDYXJlZXJQaWxvdEFJLTJcXFxcZnJvbnRlbmRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxhcHAtcm91dGVyLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxEZXYgSWRlYXNcXFxcQ2FyZWVyUGlsb3RBSS0yXFxcXGZyb250ZW5kXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcY2xpZW50LXBhZ2UuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXERldiBJZGVhc1xcXFxDYXJlZXJQaWxvdEFJLTJcXFxcZnJvbnRlbmRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxlcnJvci1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcRGV2IElkZWFzXFxcXENhcmVlclBpbG90QUktMlxcXFxmcm9udGVuZFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGxheW91dC1yb3V0ZXIuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXERldiBJZGVhc1xcXFxDYXJlZXJQaWxvdEFJLTJcXFxcZnJvbnRlbmRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxub3QtZm91bmQtYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXERldiBJZGVhc1xcXFxDYXJlZXJQaWxvdEFJLTJcXFxcZnJvbnRlbmRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDev%20Ideas%5C%5CCareerPilotAI-2%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDev%20Ideas%5C%5CCareerPilotAI-2%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDev%20Ideas%5C%5CCareerPilotAI-2%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDev%20Ideas%5C%5CCareerPilotAI-2%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDev%20Ideas%5C%5CCareerPilotAI-2%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDev%20Ideas%5C%5CCareerPilotAI-2%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/(auth)/login/page.tsx":
/*!***********************************!*\
  !*** ./app/(auth)/login/page.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoginPage),\n/* harmony export */   dynamic: () => (/* binding */ dynamic)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _lib_auth_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/auth-context */ \"(ssr)/./lib/auth-context.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(ssr)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Loader2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Loader2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* __next_internal_client_entry_do_not_use__ dynamic,default auto */ \n// Force dynamic rendering to avoid SSG issues with React 19\nconst dynamic = \"force-dynamic\";\n\n\n\n\n\n\n\n\nfunction LoginPage() {\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const { login, loading, error } = (0,_lib_auth_context__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        await login(email, password);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex min-h-screen flex-col items-center justify-center bg-muted/40 p-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                href: \"/\",\n                className: \"mb-8 flex items-center gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        className: \"h-6 w-6\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Dev Ideas\\\\CareerPilotAI-2\\\\frontend\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-xl font-bold\",\n                        children: \"CareerPilotAI\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Dev Ideas\\\\CareerPilotAI-2\\\\frontend\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Dev Ideas\\\\CareerPilotAI-2\\\\frontend\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                className: \"w-full max-w-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                        className: \"space-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                className: \"text-2xl font-bold\",\n                                children: \"Login\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Dev Ideas\\\\CareerPilotAI-2\\\\frontend\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                lineNumber: 36,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardDescription, {\n                                children: \"Enter your email and password to access your account\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Dev Ideas\\\\CareerPilotAI-2\\\\frontend\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                lineNumber: 37,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Dev Ideas\\\\CareerPilotAI-2\\\\frontend\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                className: \"space-y-4\",\n                                children: [\n                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"rounded-md bg-destructive/15 p-3 text-sm text-destructive\",\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Dev Ideas\\\\CareerPilotAI-2\\\\frontend\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                        lineNumber: 41,\n                                        columnNumber: 23\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"email\",\n                                                children: \"Email\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Dev Ideas\\\\CareerPilotAI-2\\\\frontend\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                lineNumber: 43,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"email\",\n                                                type: \"email\",\n                                                placeholder: \"<EMAIL>\",\n                                                value: email,\n                                                onChange: (e)=>setEmail(e.target.value),\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Dev Ideas\\\\CareerPilotAI-2\\\\frontend\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                lineNumber: 44,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Dev Ideas\\\\CareerPilotAI-2\\\\frontend\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                        lineNumber: 42,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"password\",\n                                                        children: \"Password\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Dev Ideas\\\\CareerPilotAI-2\\\\frontend\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                        lineNumber: 55,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/forgot-password\",\n                                                        className: \"text-xs text-muted-foreground underline-offset-4 hover:underline\",\n                                                        children: \"Forgot password?\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Dev Ideas\\\\CareerPilotAI-2\\\\frontend\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                        lineNumber: 56,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Dev Ideas\\\\CareerPilotAI-2\\\\frontend\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                lineNumber: 54,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"password\",\n                                                type: \"password\",\n                                                value: password,\n                                                onChange: (e)=>setPassword(e.target.value),\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Dev Ideas\\\\CareerPilotAI-2\\\\frontend\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                lineNumber: 63,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Dev Ideas\\\\CareerPilotAI-2\\\\frontend\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                        lineNumber: 53,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Dev Ideas\\\\CareerPilotAI-2\\\\frontend\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardFooter, {\n                                className: \"flex flex-col space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        type: \"submit\",\n                                        className: \"w-full\",\n                                        disabled: loading,\n                                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Dev Ideas\\\\CareerPilotAI-2\\\\frontend\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                    lineNumber: 76,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Logging in...\"\n                                            ]\n                                        }, void 0, true) : \"Login\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Dev Ideas\\\\CareerPilotAI-2\\\\frontend\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center text-sm text-muted-foreground\",\n                                        children: [\n                                            \"Don't have an account?\",\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                href: \"/register\",\n                                                className: \"font-medium text-primary underline-offset-4 hover:underline\",\n                                                children: \"Register\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Dev Ideas\\\\CareerPilotAI-2\\\\frontend\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                                lineNumber: 85,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Dev Ideas\\\\CareerPilotAI-2\\\\frontend\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Dev Ideas\\\\CareerPilotAI-2\\\\frontend\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Dev Ideas\\\\CareerPilotAI-2\\\\frontend\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Dev Ideas\\\\CareerPilotAI-2\\\\frontend\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Dev Ideas\\\\CareerPilotAI-2\\\\frontend\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./app/(auth)/login/page.tsx\n");

/***/ }),

/***/ "(ssr)/./app/not-found.tsx":
/*!***************************!*\
  !*** ./app/not-found.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotFound),\n/* harmony export */   dynamic: () => (/* binding */ dynamic),\n/* harmony export */   revalidate: () => (/* binding */ revalidate),\n/* harmony export */   runtime: () => (/* binding */ runtime)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* __next_internal_client_entry_do_not_use__ dynamic,runtime,revalidate,default auto */ \n\n// Force dynamic rendering to avoid SSG issues with React 19\nconst dynamic = \"force-dynamic\";\nconst runtime = \"nodejs\";\nconst revalidate = 0;\nfunction NotFound() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-6xl font-bold text-gray-900 mb-4\",\n                    children: \"404\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Dev Ideas\\\\CareerPilotAI-2\\\\frontend\\\\app\\\\not-found.tsx\",\n                    lineNumber: 14,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-semibold text-gray-700 mb-4\",\n                    children: \"Page Not Found\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Dev Ideas\\\\CareerPilotAI-2\\\\frontend\\\\app\\\\not-found.tsx\",\n                    lineNumber: 15,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600 mb-8\",\n                    children: \"The page you are looking for doesn't exist or has been moved.\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Dev Ideas\\\\CareerPilotAI-2\\\\frontend\\\\app\\\\not-found.tsx\",\n                    lineNumber: 16,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    href: \"/\",\n                    className: \"inline-flex items-center px-4 py-2 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                    children: \"Go back home\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Dev Ideas\\\\CareerPilotAI-2\\\\frontend\\\\app\\\\not-found.tsx\",\n                    lineNumber: 19,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Dev Ideas\\\\CareerPilotAI-2\\\\frontend\\\\app\\\\not-found.tsx\",\n            lineNumber: 13,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Dev Ideas\\\\CareerPilotAI-2\\\\frontend\\\\app\\\\not-found.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/not-found.tsx\n");

/***/ }),

/***/ "(ssr)/./components/theme-provider.tsx":
/*!***************************************!*\
  !*** ./components/theme-provider.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider auto */ \n\n\nfunction ThemeProvider({ children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n        ...props,\n        enableSystem: true,\n        attribute: \"class\",\n        defaultTheme: \"system\",\n        enableColorScheme: true,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Dev Ideas\\\\CareerPilotAI-2\\\\frontend\\\\components\\\\theme-provider.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3RoZW1lLXByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBRThCO0FBSVY7QUFFYixTQUFTQyxjQUFjLEVBQUVFLFFBQVEsRUFBRSxHQUFHQyxPQUEyQjtJQUN0RSxxQkFDRSw4REFBQ0Ysc0RBQWtCQTtRQUNoQixHQUFHRSxLQUFLO1FBQ1RDLFlBQVk7UUFDWkMsV0FBVTtRQUNWQyxjQUFhO1FBQ2JDLGlCQUFpQjtrQkFFaEJMOzs7Ozs7QUFHUCIsInNvdXJjZXMiOlsid2VicGFjazovL215LXYwLXByb2plY3QvLi9jb21wb25lbnRzL3RoZW1lLXByb3ZpZGVyLnRzeD85Mjg5Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tICdyZWFjdCdcbmltcG9ydCB7XG4gIFRoZW1lUHJvdmlkZXIgYXMgTmV4dFRoZW1lc1Byb3ZpZGVyLFxuICB0eXBlIFRoZW1lUHJvdmlkZXJQcm9wcyxcbn0gZnJvbSAnbmV4dC10aGVtZXMnXG5cbmV4cG9ydCBmdW5jdGlvbiBUaGVtZVByb3ZpZGVyKHsgY2hpbGRyZW4sIC4uLnByb3BzIH06IFRoZW1lUHJvdmlkZXJQcm9wcykge1xuICByZXR1cm4gKFxuICAgIDxOZXh0VGhlbWVzUHJvdmlkZXJcbiAgICAgIHsuLi5wcm9wc31cbiAgICAgIGVuYWJsZVN5c3RlbVxuICAgICAgYXR0cmlidXRlPVwiY2xhc3NcIlxuICAgICAgZGVmYXVsdFRoZW1lPVwic3lzdGVtXCJcbiAgICAgIGVuYWJsZUNvbG9yU2NoZW1lXG4gICAgPlxuICAgICAge2NoaWxkcmVufVxuICAgIDwvTmV4dFRoZW1lc1Byb3ZpZGVyPlxuICApXG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJUaGVtZVByb3ZpZGVyIiwiTmV4dFRoZW1lc1Byb3ZpZGVyIiwiY2hpbGRyZW4iLCJwcm9wcyIsImVuYWJsZVN5c3RlbSIsImF0dHJpYnV0ZSIsImRlZmF1bHRUaGVtZSIsImVuYWJsZUNvbG9yU2NoZW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/theme-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 active:scale-[0.98]\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground shadow-sm hover:bg-primary/90 hover:shadow-md\",\n            destructive: \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90 hover:shadow-md\",\n            outline: \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground hover:shadow-md hover:border-accent\",\n            secondary: \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80 hover:shadow-md\",\n            ghost: \"hover:bg-accent/50 hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\",\n            gradient: \"bg-gradient-to-r from-blue-600 to-indigo-600 text-white shadow-md hover:shadow-lg hover:from-blue-700 hover:to-indigo-700\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Dev Ideas\\\\CareerPilotAI-2\\\\frontend\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 47,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/card.tsx":
/*!********************************!*\
  !*** ./components/ui/card.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm transition-all duration-200 hover:shadow-md\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Dev Ideas\\\\CareerPilotAI-2\\\\frontend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Dev Ideas\\\\CareerPilotAI-2\\\\frontend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Dev Ideas\\\\CareerPilotAI-2\\\\frontend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Dev Ideas\\\\CareerPilotAI-2\\\\frontend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 51,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Dev Ideas\\\\CareerPilotAI-2\\\\frontend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 63,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Dev Ideas\\\\CareerPilotAI-2\\\\frontend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 71,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/input.tsx":
/*!*********************************!*\
  !*** ./components/ui/input.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, type, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Dev Ideas\\\\CareerPilotAI-2\\\\frontend\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 8,\n        columnNumber: 7\n    }, undefined);\n});\nInput.displayName = \"Input\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2lucHV0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQThCO0FBRUU7QUFFaEMsTUFBTUUsc0JBQVFGLDZDQUFnQixDQUM1QixDQUFDLEVBQUVJLFNBQVMsRUFBRUMsSUFBSSxFQUFFLEdBQUdDLE9BQU8sRUFBRUM7SUFDOUIscUJBQ0UsOERBQUNDO1FBQ0NILE1BQU1BO1FBQ05ELFdBQVdILDhDQUFFQSxDQUNYLGtZQUNBRztRQUVGRyxLQUFLQTtRQUNKLEdBQUdELEtBQUs7Ozs7OztBQUdmO0FBRUZKLE1BQU1PLFdBQVcsR0FBRztBQUVKIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbXktdjAtcHJvamVjdC8uL2NvbXBvbmVudHMvdWkvaW5wdXQudHN4P2RhNzkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCJcblxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxuXG5jb25zdCBJbnB1dCA9IFJlYWN0LmZvcndhcmRSZWY8SFRNTElucHV0RWxlbWVudCwgUmVhY3QuQ29tcG9uZW50UHJvcHM8XCJpbnB1dFwiPj4oXG4gICh7IGNsYXNzTmFtZSwgdHlwZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxpbnB1dFxuICAgICAgICB0eXBlPXt0eXBlfVxuICAgICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICAgIFwiZmxleCBoLTEwIHctZnVsbCByb3VuZGVkLW1kIGJvcmRlciBib3JkZXItaW5wdXQgYmctYmFja2dyb3VuZCBweC0zIHB5LTIgdGV4dC1iYXNlIHJpbmctb2Zmc2V0LWJhY2tncm91bmQgZmlsZTpib3JkZXItMCBmaWxlOmJnLXRyYW5zcGFyZW50IGZpbGU6dGV4dC1zbSBmaWxlOmZvbnQtbWVkaXVtIGZpbGU6dGV4dC1mb3JlZ3JvdW5kIHBsYWNlaG9sZGVyOnRleHQtbXV0ZWQtZm9yZWdyb3VuZCBmb2N1cy12aXNpYmxlOm91dGxpbmUtbm9uZSBmb2N1cy12aXNpYmxlOnJpbmctMiBmb2N1cy12aXNpYmxlOnJpbmctcmluZyBmb2N1cy12aXNpYmxlOnJpbmctb2Zmc2V0LTIgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkIGRpc2FibGVkOm9wYWNpdHktNTAgbWQ6dGV4dC1zbVwiLFxuICAgICAgICAgIGNsYXNzTmFtZVxuICAgICAgICApfVxuICAgICAgICByZWY9e3JlZn1cbiAgICAgICAgey4uLnByb3BzfVxuICAgICAgLz5cbiAgICApXG4gIH1cbilcbklucHV0LmRpc3BsYXlOYW1lID0gXCJJbnB1dFwiXG5cbmV4cG9ydCB7IElucHV0IH1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsImNuIiwiSW5wdXQiLCJmb3J3YXJkUmVmIiwiY2xhc3NOYW1lIiwidHlwZSIsInByb3BzIiwicmVmIiwiaW5wdXQiLCJkaXNwbGF5TmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/input.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/label.tsx":
/*!*********************************!*\
  !*** ./components/ui/label.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Label: () => (/* binding */ Label)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-label */ \"(ssr)/./node_modules/@radix-ui/react-label/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Label auto */ \n\n\n\n\nconst labelVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\");\nconst Label = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(labelVariants(), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Dev Ideas\\\\CareerPilotAI-2\\\\frontend\\\\components\\\\ui\\\\label.tsx\",\n        lineNumber: 18,\n        columnNumber: 3\n    }, undefined));\nLabel.displayName = _radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__.Root.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2xhYmVsLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFFOEI7QUFDeUI7QUFDVTtBQUVqQztBQUVoQyxNQUFNSSxnQkFBZ0JGLDZEQUFHQSxDQUN2QjtBQUdGLE1BQU1HLHNCQUFRTCw2Q0FBZ0IsQ0FJNUIsQ0FBQyxFQUFFTyxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQyxvQkFDMUIsOERBQUNSLHVEQUFtQjtRQUNsQlEsS0FBS0E7UUFDTEYsV0FBV0osOENBQUVBLENBQUNDLGlCQUFpQkc7UUFDOUIsR0FBR0MsS0FBSzs7Ozs7O0FBR2JILE1BQU1NLFdBQVcsR0FBR1YsdURBQW1CLENBQUNVLFdBQVc7QUFFbkMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9teS12MC1wcm9qZWN0Ly4vY29tcG9uZW50cy91aS9sYWJlbC50c3g/ODhlZCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIlxuXG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxuaW1wb3J0ICogYXMgTGFiZWxQcmltaXRpdmUgZnJvbSBcIkByYWRpeC11aS9yZWFjdC1sYWJlbFwiXG5pbXBvcnQgeyBjdmEsIHR5cGUgVmFyaWFudFByb3BzIH0gZnJvbSBcImNsYXNzLXZhcmlhbmNlLWF1dGhvcml0eVwiXG5cbmltcG9ydCB7IGNuIH0gZnJvbSBcIkAvbGliL3V0aWxzXCJcblxuY29uc3QgbGFiZWxWYXJpYW50cyA9IGN2YShcbiAgXCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIGxlYWRpbmctbm9uZSBwZWVyLWRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZCBwZWVyLWRpc2FibGVkOm9wYWNpdHktNzBcIlxuKVxuXG5jb25zdCBMYWJlbCA9IFJlYWN0LmZvcndhcmRSZWY8XG4gIFJlYWN0LkVsZW1lbnRSZWY8dHlwZW9mIExhYmVsUHJpbWl0aXZlLlJvb3Q+LFxuICBSZWFjdC5Db21wb25lbnRQcm9wc1dpdGhvdXRSZWY8dHlwZW9mIExhYmVsUHJpbWl0aXZlLlJvb3Q+ICZcbiAgICBWYXJpYW50UHJvcHM8dHlwZW9mIGxhYmVsVmFyaWFudHM+XG4+KCh7IGNsYXNzTmFtZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiAoXG4gIDxMYWJlbFByaW1pdGl2ZS5Sb290XG4gICAgcmVmPXtyZWZ9XG4gICAgY2xhc3NOYW1lPXtjbihsYWJlbFZhcmlhbnRzKCksIGNsYXNzTmFtZSl9XG4gICAgey4uLnByb3BzfVxuICAvPlxuKSlcbkxhYmVsLmRpc3BsYXlOYW1lID0gTGFiZWxQcmltaXRpdmUuUm9vdC5kaXNwbGF5TmFtZVxuXG5leHBvcnQgeyBMYWJlbCB9XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJMYWJlbFByaW1pdGl2ZSIsImN2YSIsImNuIiwibGFiZWxWYXJpYW50cyIsIkxhYmVsIiwiZm9yd2FyZFJlZiIsImNsYXNzTmFtZSIsInByb3BzIiwicmVmIiwiUm9vdCIsImRpc3BsYXlOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/label.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/toast.tsx":
/*!*********************************!*\
  !*** ./components/ui/toast.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toast: () => (/* binding */ Toast),\n/* harmony export */   ToastAction: () => (/* binding */ ToastAction),\n/* harmony export */   ToastClose: () => (/* binding */ ToastClose),\n/* harmony export */   ToastDescription: () => (/* binding */ ToastDescription),\n/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider),\n/* harmony export */   ToastTitle: () => (/* binding */ ToastTitle),\n/* harmony export */   ToastViewport: () => (/* binding */ ToastViewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-toast */ \"(ssr)/./node_modules/@radix-ui/react-toast/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ ToastProvider,ToastViewport,Toast,ToastTitle,ToastDescription,ToastClose,ToastAction auto */ \n\n\n\n\n\nconst ToastProvider = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Provider;\nconst ToastViewport = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Viewport, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"fixed bottom-0 right-0 z-[100] flex max-h-screen w-full flex-col-reverse gap-2 p-4 sm:max-w-[420px]\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Dev Ideas\\\\CareerPilotAI-2\\\\frontend\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 16,\n        columnNumber: 3\n    }, undefined));\nToastViewport.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Viewport.displayName;\nconst toastVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-5 pr-7 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-right-full mb-2\", {\n    variants: {\n        variant: {\n            default: \"border bg-background text-foreground shadow-md\",\n            destructive: \"destructive group border-destructive bg-destructive text-destructive-foreground shadow-md\",\n            success: \"border-green-500 bg-green-50 text-green-900 dark:bg-green-950 dark:text-green-100 shadow-md\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nconst Toast = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(toastVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Dev Ideas\\\\CareerPilotAI-2\\\\frontend\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, undefined);\n});\nToast.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Root.displayName;\nconst ToastAction = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Action, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Dev Ideas\\\\CareerPilotAI-2\\\\frontend\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 63,\n        columnNumber: 3\n    }, undefined));\nToastAction.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Action.displayName;\nconst ToastClose = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Close, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600\", className),\n        \"toast-close\": \"\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"D:\\\\Dev Ideas\\\\CareerPilotAI-2\\\\frontend\\\\components\\\\ui\\\\toast.tsx\",\n            lineNumber: 87,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Dev Ideas\\\\CareerPilotAI-2\\\\frontend\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 78,\n        columnNumber: 3\n    }, undefined));\nToastClose.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Close.displayName;\nconst ToastTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Title, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm font-semibold\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Dev Ideas\\\\CareerPilotAI-2\\\\frontend\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 96,\n        columnNumber: 3\n    }, undefined));\nToastTitle.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Title.displayName;\nconst ToastDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Description, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm opacity-90\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Dev Ideas\\\\CareerPilotAI-2\\\\frontend\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 108,\n        columnNumber: 3\n    }, undefined));\nToastDescription.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Description.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/toast.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/toaster.tsx":
/*!***********************************!*\
  !*** ./components/ui/toaster.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toaster: () => (/* binding */ Toaster)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/hooks/use-toast */ \"(ssr)/./hooks/use-toast.ts\");\n/* harmony import */ var _components_ui_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/toast */ \"(ssr)/./components/ui/toast.tsx\");\n/* __next_internal_client_entry_do_not_use__ Toaster auto */ \n\n\nfunction Toaster() {\n    const { toasts } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_1__.useToast)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_2__.ToastProvider, {\n        children: [\n            toasts.map(function({ id, title, description, action, ...props }) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_2__.Toast, {\n                    ...props,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid gap-1\",\n                            children: [\n                                title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_2__.ToastTitle, {\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Dev Ideas\\\\CareerPilotAI-2\\\\frontend\\\\components\\\\ui\\\\toaster.tsx\",\n                                    lineNumber: 22,\n                                    columnNumber: 25\n                                }, this),\n                                description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_2__.ToastDescription, {\n                                    children: description\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Dev Ideas\\\\CareerPilotAI-2\\\\frontend\\\\components\\\\ui\\\\toaster.tsx\",\n                                    lineNumber: 24,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Dev Ideas\\\\CareerPilotAI-2\\\\frontend\\\\components\\\\ui\\\\toaster.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 13\n                        }, this),\n                        action,\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_2__.ToastClose, {}, void 0, false, {\n                            fileName: \"D:\\\\Dev Ideas\\\\CareerPilotAI-2\\\\frontend\\\\components\\\\ui\\\\toaster.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, id, true, {\n                    fileName: \"D:\\\\Dev Ideas\\\\CareerPilotAI-2\\\\frontend\\\\components\\\\ui\\\\toaster.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 11\n                }, this);\n            }),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_2__.ToastViewport, {}, void 0, false, {\n                fileName: \"D:\\\\Dev Ideas\\\\CareerPilotAI-2\\\\frontend\\\\components\\\\ui\\\\toaster.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Dev Ideas\\\\CareerPilotAI-2\\\\frontend\\\\components\\\\ui\\\\toaster.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/toaster.tsx\n");

/***/ }),

/***/ "(ssr)/./hooks/use-toast.ts":
/*!****************************!*\
  !*** ./hooks/use-toast.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reducer: () => (/* binding */ reducer),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ reducer,useToast,toast auto */ // Inspired by react-hot-toast library\n\nconst TOAST_LIMIT = 3;\nconst TOAST_REMOVE_DELAY = 5000 // 5 seconds default\n;\nconst actionTypes = {\n    ADD_TOAST: \"ADD_TOAST\",\n    UPDATE_TOAST: \"UPDATE_TOAST\",\n    DISMISS_TOAST: \"DISMISS_TOAST\",\n    REMOVE_TOAST: \"REMOVE_TOAST\"\n};\nlet count = 0;\nfunction genId() {\n    count = (count + 1) % Number.MAX_SAFE_INTEGER;\n    return count.toString();\n}\nconst toastTimeouts = new Map();\nconst addToRemoveQueue = (toastId)=>{\n    if (toastTimeouts.has(toastId)) {\n        return;\n    }\n    const timeout = setTimeout(()=>{\n        toastTimeouts.delete(toastId);\n        dispatch({\n            type: \"REMOVE_TOAST\",\n            toastId: toastId\n        });\n    }, TOAST_REMOVE_DELAY);\n    toastTimeouts.set(toastId, timeout);\n};\nconst reducer = (state, action)=>{\n    switch(action.type){\n        case \"ADD_TOAST\":\n            return {\n                ...state,\n                toasts: [\n                    action.toast,\n                    ...state.toasts\n                ].slice(0, TOAST_LIMIT)\n            };\n        case \"UPDATE_TOAST\":\n            return {\n                ...state,\n                toasts: state.toasts.map((t)=>t.id === action.toast.id ? {\n                        ...t,\n                        ...action.toast\n                    } : t)\n            };\n        case \"DISMISS_TOAST\":\n            {\n                const { toastId } = action;\n                // ! Side effects ! - This could be extracted into a dismissToast() action,\n                // but I'll keep it here for simplicity\n                if (toastId) {\n                    addToRemoveQueue(toastId);\n                } else {\n                    state.toasts.forEach((toast)=>{\n                        addToRemoveQueue(toast.id);\n                    });\n                }\n                return {\n                    ...state,\n                    toasts: state.toasts.map((t)=>t.id === toastId || toastId === undefined ? {\n                            ...t,\n                            open: false\n                        } : t)\n                };\n            }\n        case \"REMOVE_TOAST\":\n            if (action.toastId === undefined) {\n                return {\n                    ...state,\n                    toasts: []\n                };\n            }\n            return {\n                ...state,\n                toasts: state.toasts.filter((t)=>t.id !== action.toastId)\n            };\n    }\n};\nconst listeners = [];\nlet memoryState = {\n    toasts: []\n};\nfunction dispatch(action) {\n    memoryState = reducer(memoryState, action);\n    listeners.forEach((listener)=>{\n        listener(memoryState);\n    });\n}\n// Store recent toast keys to prevent duplicates\nconst recentToastKeys = new Set();\nfunction toast({ duration, key, ...props }) {\n    const id = genId();\n    // If a key is provided, check if we've shown this toast recently\n    if (key) {\n        // If this exact toast was shown recently, don't show it again\n        if (recentToastKeys.has(key)) {\n            return {\n                id,\n                dismiss: ()=>{},\n                update: ()=>{}\n            };\n        }\n        // Add this key to recent toasts\n        recentToastKeys.add(key);\n        // Remove the key after a delay (to prevent showing the same toast repeatedly)\n        setTimeout(()=>{\n            recentToastKeys.delete(key);\n        }, 3000) // Prevent duplicates for 3 seconds\n        ;\n    }\n    const update = (props)=>dispatch({\n            type: \"UPDATE_TOAST\",\n            toast: {\n                ...props,\n                id\n            }\n        });\n    const dismiss = ()=>dispatch({\n            type: \"DISMISS_TOAST\",\n            toastId: id\n        });\n    // Use custom duration if provided\n    if (duration) {\n        const customTimeout = setTimeout(()=>{\n            dismiss();\n        }, duration);\n        // Clean up timeout if toast is dismissed manually\n        const originalOnOpenChange = props.onOpenChange;\n        props.onOpenChange = (open)=>{\n            if (!open) {\n                clearTimeout(customTimeout);\n                dismiss();\n            }\n            originalOnOpenChange?.(open);\n        };\n    }\n    dispatch({\n        type: \"ADD_TOAST\",\n        toast: {\n            ...props,\n            id,\n            open: true,\n            onOpenChange: (open)=>{\n                if (!open) dismiss();\n            }\n        }\n    });\n    return {\n        id: id,\n        dismiss,\n        update\n    };\n}\nfunction useToast() {\n    const [state, setState] = react__WEBPACK_IMPORTED_MODULE_0__.useState(memoryState);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        listeners.push(setState);\n        return ()=>{\n            const index = listeners.indexOf(setState);\n            if (index > -1) {\n                listeners.splice(index, 1);\n            }\n        };\n    }, [\n        state\n    ]);\n    return {\n        ...state,\n        toast,\n        dismiss: (toastId)=>dispatch({\n                type: \"DISMISS_TOAST\",\n                toastId\n            })\n    };\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./hooks/use-toast.ts\n");

/***/ }),

/***/ "(ssr)/./lib/auth-context.tsx":
/*!******************************!*\
  !*** ./lib/auth-context.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_secure_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/secure-api */ \"(ssr)/./lib/secure-api.ts\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/use-toast */ \"(ssr)/./hooks/use-toast.ts\");\n/* harmony import */ var _lib_secure_auth__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/secure-auth */ \"(ssr)/./lib/secure-auth.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const [authState, setAuthState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        user: null,\n        isAuthenticated: false,\n        loading: true,\n        error: null\n    });\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__.useToast)();\n    // Load user on initial render using secure authentication\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadUser = async ()=>{\n            try {\n                const res = await _lib_secure_api__WEBPACK_IMPORTED_MODULE_3__.secureAuthAPI.getCurrentUser();\n                if (res.success && res.isAuthenticated) {\n                    setAuthState({\n                        user: res.user,\n                        isAuthenticated: true,\n                        loading: false,\n                        error: null\n                    });\n                } else {\n                    // Not authenticated, but not an error\n                    setAuthState({\n                        user: null,\n                        isAuthenticated: false,\n                        loading: false,\n                        error: null\n                    });\n                }\n            } catch (err) {\n                console.error(\"Error loading user:\", err);\n                setAuthState({\n                    user: null,\n                    isAuthenticated: false,\n                    loading: false,\n                    error: null\n                });\n            }\n        };\n        loadUser();\n    }, []);\n    // Register user with enhanced security\n    const register = async (name, email, password)=>{\n        setAuthState((prev)=>({\n                ...prev,\n                loading: true,\n                error: null\n            }));\n        try {\n            // Client-side validation\n            if (!name.trim()) {\n                throw new Error(\"Name is required\");\n            }\n            if (!(0,_lib_secure_auth__WEBPACK_IMPORTED_MODULE_5__.isValidEmail)(email)) {\n                throw new Error(\"Please enter a valid email address\");\n            }\n            const passwordValidation = (0,_lib_secure_auth__WEBPACK_IMPORTED_MODULE_5__.validatePassword)(password);\n            if (!passwordValidation.isValid) {\n                throw new Error(passwordValidation.errors[0]);\n            }\n            // Rate limiting check\n            if (!_lib_secure_auth__WEBPACK_IMPORTED_MODULE_5__.authRateLimiter.isAllowed(\"register\")) {\n                throw new Error(\"Too many registration attempts. Please try again later.\");\n            }\n            const res = await _lib_secure_api__WEBPACK_IMPORTED_MODULE_3__.secureAuthAPI.register(name, email, password);\n            // Update auth state with user data (no token stored in localStorage)\n            setAuthState({\n                user: res.user,\n                isAuthenticated: true,\n                loading: false,\n                error: null\n            });\n            toast({\n                title: \"Registration successful\",\n                description: \"Welcome to CareerPilotAI!\"\n            });\n            router.push(\"/dashboard\");\n        } catch (err) {\n            console.error(\"Registration error:\", err);\n            setAuthState((prev)=>({\n                    ...prev,\n                    loading: false,\n                    error: err.message || \"Registration failed\"\n                }));\n            toast({\n                title: \"Registration failed\",\n                description: err.message || \"Please try again\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    // Login user with enhanced security\n    const login = async (email, password)=>{\n        setAuthState((prev)=>({\n                ...prev,\n                loading: true,\n                error: null\n            }));\n        try {\n            // Client-side validation\n            if (!(0,_lib_secure_auth__WEBPACK_IMPORTED_MODULE_5__.isValidEmail)(email)) {\n                throw new Error(\"Please enter a valid email address\");\n            }\n            if (!password.trim()) {\n                throw new Error(\"Password is required\");\n            }\n            // Rate limiting check\n            if (!_lib_secure_auth__WEBPACK_IMPORTED_MODULE_5__.authRateLimiter.isAllowed(\"login\")) {\n                throw new Error(\"Too many login attempts. Please try again later.\");\n            }\n            const res = await _lib_secure_api__WEBPACK_IMPORTED_MODULE_3__.secureAuthAPI.login(email, password);\n            // Update auth state with user data (no token stored in localStorage)\n            setAuthState({\n                user: res.user,\n                isAuthenticated: true,\n                loading: false,\n                error: null\n            });\n            toast({\n                title: \"Login successful\",\n                description: \"Welcome back!\"\n            });\n            router.push(\"/dashboard\");\n        } catch (err) {\n            console.error(\"Login error:\", err);\n            setAuthState((prev)=>({\n                    ...prev,\n                    loading: false,\n                    error: err.message || \"Login failed\"\n                }));\n            toast({\n                title: \"Login failed\",\n                description: err.message || \"Invalid credentials\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    // Logout user with secure cleanup\n    const logout = async ()=>{\n        try {\n            await _lib_secure_api__WEBPACK_IMPORTED_MODULE_3__.secureAuthAPI.logout();\n        } catch (err) {\n            console.error(\"Logout error:\", err);\n        } finally{\n            // Clear auth state (httpOnly cookie is cleared by the API)\n            setAuthState({\n                user: null,\n                isAuthenticated: false,\n                loading: false,\n                error: null\n            });\n            toast({\n                title: \"Logged out\",\n                description: \"You have been logged out successfully\"\n            });\n            router.push(\"/auth/login\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            ...authState,\n            login,\n            register,\n            logout\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Dev Ideas\\\\CareerPilotAI-2\\\\frontend\\\\lib\\\\auth-context.tsx\",\n        lineNumber: 199,\n        columnNumber: 5\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/auth-context.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/secure-api.ts":
/*!***************************!*\
  !*** ./lib/secure-api.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   secureAuthAPI: () => (/* binding */ secureAuthAPI),\n/* harmony export */   secureCoverLetterAPI: () => (/* binding */ secureCoverLetterAPI),\n/* harmony export */   secureExportAPI: () => (/* binding */ secureExportAPI),\n/* harmony export */   secureLinkedInAPI: () => (/* binding */ secureLinkedInAPI),\n/* harmony export */   secureResumeAPI: () => (/* binding */ secureResumeAPI),\n/* harmony export */   secureUserAPI: () => (/* binding */ secureUserAPI)\n/* harmony export */ });\n/* harmony import */ var _secure_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./secure-auth */ \"(ssr)/./lib/secure-auth.ts\");\n/**\n * Secure API client using httpOnly cookies instead of localStorage tokens\n */ \n// API configuration\nconst API_BASE_URL = \"http://localhost:5000\" || 0;\n/**\n * Secure API request wrapper\n */ async function secureRequest(endpoint, options = {}) {\n    const url = endpoint.startsWith(\"http\") ? endpoint : `${API_BASE_URL}${endpoint}`;\n    // Rate limiting check\n    const clientId = \"client\"; // In production, use a more specific identifier\n    if (!_secure_auth__WEBPACK_IMPORTED_MODULE_0__.authRateLimiter.isAllowed(clientId)) {\n        throw new Error(\"Too many requests. Please try again later.\");\n    }\n    const csrfToken = await (0,_secure_auth__WEBPACK_IMPORTED_MODULE_0__.getCSRFToken)();\n    const defaultHeaders = {\n        \"Content-Type\": \"application/json\",\n        \"X-CSRF-Token\": csrfToken\n    };\n    const mergedOptions = {\n        ...options,\n        credentials: \"include\",\n        headers: {\n            ...defaultHeaders,\n            ...options.headers\n        }\n    };\n    try {\n        const response = await fetch(url, mergedOptions);\n        // Handle authentication errors\n        if (response.status === 401) {\n            // Token expired or invalid, redirect to login\n            if (false) {}\n            throw new Error(\"Authentication required\");\n        }\n        return response;\n    } catch (error) {\n        console.error(\"API request error:\", error);\n        throw error;\n    }\n}\n/**\n * Secure authentication API\n */ const secureAuthAPI = {\n    async login (email, password) {\n        const csrfToken = await (0,_secure_auth__WEBPACK_IMPORTED_MODULE_0__.getCSRFToken)();\n        const response = await fetch(\"/api/auth/login\", {\n            method: \"POST\",\n            credentials: \"include\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                \"X-CSRF-Token\": csrfToken\n            },\n            body: JSON.stringify({\n                email,\n                password\n            })\n        });\n        if (!response.ok) {\n            const error = await response.json();\n            throw new Error(error.message || \"Login failed\");\n        }\n        return response.json();\n    },\n    async register (name, email, password) {\n        const csrfToken = await (0,_secure_auth__WEBPACK_IMPORTED_MODULE_0__.getCSRFToken)();\n        const response = await fetch(\"/api/auth/register\", {\n            method: \"POST\",\n            credentials: \"include\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                \"X-CSRF-Token\": csrfToken\n            },\n            body: JSON.stringify({\n                name,\n                email,\n                password\n            })\n        });\n        if (!response.ok) {\n            const error = await response.json();\n            throw new Error(error.message || \"Registration failed\");\n        }\n        return response.json();\n    },\n    async logout () {\n        const csrfToken = await (0,_secure_auth__WEBPACK_IMPORTED_MODULE_0__.getCSRFToken)();\n        const response = await fetch(\"/api/auth/logout\", {\n            method: \"POST\",\n            credentials: \"include\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                \"X-CSRF-Token\": csrfToken\n            }\n        });\n        if (!response.ok) {\n            const error = await response.json();\n            throw new Error(error.message || \"Logout failed\");\n        }\n        return response.json();\n    },\n    async getCurrentUser () {\n        const response = await fetch(\"/api/auth/status\", {\n            method: \"GET\",\n            credentials: \"include\"\n        });\n        if (!response.ok) {\n            // If it's a 401, it just means not authenticated (not an error)\n            if (response.status === 401) {\n                return {\n                    success: false,\n                    isAuthenticated: false,\n                    user: null\n                };\n            }\n            const error = await response.json();\n            throw new Error(error.message || \"Failed to get current user\");\n        }\n        return response.json();\n    }\n};\n/**\n * Secure resume API\n */ const secureResumeAPI = {\n    async generateResume (data) {\n        const response = await secureRequest(\"/api/generate/resume\", {\n            method: \"POST\",\n            body: JSON.stringify(data)\n        });\n        if (!response.ok) {\n            const error = await response.json();\n            throw new Error(error.message || \"Resume generation failed\");\n        }\n        return response.json();\n    },\n    async getResumes (page = 1, limit = 10) {\n        const response = await secureRequest(`/api/generate/resume?page=${page}&limit=${limit}`);\n        if (!response.ok) {\n            const error = await response.json();\n            throw new Error(error.message || \"Failed to fetch resumes\");\n        }\n        return response.json();\n    },\n    async getResume (id) {\n        const response = await secureRequest(`/api/generate/resume/${id}`);\n        if (!response.ok) {\n            const error = await response.json();\n            throw new Error(error.message || \"Failed to fetch resume\");\n        }\n        return response.json();\n    },\n    async updateResume (id, data) {\n        const response = await secureRequest(`/api/generate/resume/${id}`, {\n            method: \"PUT\",\n            body: JSON.stringify(data)\n        });\n        if (!response.ok) {\n            const error = await response.json();\n            throw new Error(error.message || \"Resume update failed\");\n        }\n        return response.json();\n    },\n    async deleteResume (id) {\n        const response = await secureRequest(`/api/generate/resume/${id}`, {\n            method: \"DELETE\"\n        });\n        if (!response.ok) {\n            const error = await response.json();\n            throw new Error(error.message || \"Resume deletion failed\");\n        }\n        return response.json();\n    }\n};\n/**\n * Secure cover letter API\n */ const secureCoverLetterAPI = {\n    async generateCoverLetter (data) {\n        const response = await secureRequest(\"/api/generate/cover-letter\", {\n            method: \"POST\",\n            body: JSON.stringify(data)\n        });\n        if (!response.ok) {\n            const error = await response.json();\n            throw new Error(error.message || \"Cover letter generation failed\");\n        }\n        return response.json();\n    },\n    async getCoverLetters (page = 1, limit = 10) {\n        const response = await secureRequest(`/api/generate/cover-letter?page=${page}&limit=${limit}`);\n        if (!response.ok) {\n            const error = await response.json();\n            throw new Error(error.message || \"Failed to fetch cover letters\");\n        }\n        return response.json();\n    },\n    async getCoverLetter (id) {\n        const response = await secureRequest(`/api/generate/cover-letter/${id}`);\n        if (!response.ok) {\n            const error = await response.json();\n            throw new Error(error.message || \"Failed to fetch cover letter\");\n        }\n        return response.json();\n    },\n    async updateCoverLetter (id, data) {\n        const response = await secureRequest(`/api/generate/cover-letter/${id}`, {\n            method: \"PUT\",\n            body: JSON.stringify(data)\n        });\n        if (!response.ok) {\n            const error = await response.json();\n            throw new Error(error.message || \"Cover letter update failed\");\n        }\n        return response.json();\n    },\n    async deleteCoverLetter (id) {\n        const response = await secureRequest(`/api/generate/cover-letter/${id}`, {\n            method: \"DELETE\"\n        });\n        if (!response.ok) {\n            const error = await response.json();\n            throw new Error(error.message || \"Cover letter deletion failed\");\n        }\n        return response.json();\n    }\n};\n/**\n * Secure LinkedIn API\n */ const secureLinkedInAPI = {\n    async generateLinkedInBio (data) {\n        const response = await secureRequest(\"/api/generate/linkedin\", {\n            method: \"POST\",\n            body: JSON.stringify(data)\n        });\n        if (!response.ok) {\n            const error = await response.json();\n            throw new Error(error.message || \"LinkedIn bio generation failed\");\n        }\n        return response.json();\n    },\n    async getLinkedInBios (page = 1, limit = 10) {\n        const response = await secureRequest(`/api/generate/linkedin?page=${page}&limit=${limit}`);\n        if (!response.ok) {\n            const error = await response.json();\n            throw new Error(error.message || \"Failed to fetch LinkedIn bios\");\n        }\n        return response.json();\n    },\n    async getLinkedInBio (id) {\n        const response = await secureRequest(`/api/generate/linkedin/${id}`);\n        if (!response.ok) {\n            const error = await response.json();\n            throw new Error(error.message || \"Failed to fetch LinkedIn bio\");\n        }\n        return response.json();\n    },\n    async updateLinkedInBio (id, data) {\n        const response = await secureRequest(`/api/generate/linkedin/${id}`, {\n            method: \"PUT\",\n            body: JSON.stringify(data)\n        });\n        if (!response.ok) {\n            const error = await response.json();\n            throw new Error(error.message || \"LinkedIn bio update failed\");\n        }\n        return response.json();\n    },\n    async deleteLinkedInBio (id) {\n        const response = await secureRequest(`/api/generate/linkedin/${id}`, {\n            method: \"DELETE\"\n        });\n        if (!response.ok) {\n            const error = await response.json();\n            throw new Error(error.message || \"LinkedIn bio deletion failed\");\n        }\n        return response.json();\n    }\n};\n/**\n * Secure export API\n */ const secureExportAPI = {\n    async exportToPDF (type, id) {\n        const response = await secureRequest(`/api/export/pdf/${type}/${id}`, {\n            method: \"GET\"\n        });\n        if (!response.ok) {\n            const error = await response.json();\n            throw new Error(error.message || \"PDF export failed\");\n        }\n        return response.blob();\n    },\n    async exportToWord (type, id) {\n        const response = await secureRequest(`/api/export/docx/${type}/${id}`, {\n            method: \"GET\"\n        });\n        if (!response.ok) {\n            const error = await response.json();\n            throw new Error(error.message || \"Word export failed\");\n        }\n        return response.blob();\n    }\n};\n/**\n * Secure user API\n */ const secureUserAPI = {\n    async getProfile () {\n        const response = await secureRequest(\"/api/users/profile\");\n        if (!response.ok) {\n            const error = await response.json();\n            throw new Error(error.message || \"Failed to fetch profile\");\n        }\n        return response.json();\n    },\n    async updateProfile (data) {\n        const response = await secureRequest(\"/api/users/profile\", {\n            method: \"PUT\",\n            body: JSON.stringify(data)\n        });\n        if (!response.ok) {\n            const error = await response.json();\n            throw new Error(error.message || \"Profile update failed\");\n        }\n        return response.json();\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/secure-api.ts\n");

/***/ }),

/***/ "(ssr)/./lib/secure-auth.ts":
/*!****************************!*\
  !*** ./lib/secure-auth.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authRateLimiter: () => (/* binding */ authRateLimiter),\n/* harmony export */   checkAuthStatus: () => (/* binding */ checkAuthStatus),\n/* harmony export */   generateCSRFToken: () => (/* binding */ generateCSRFToken),\n/* harmony export */   getCSRFToken: () => (/* binding */ getCSRFToken),\n/* harmony export */   isValidEmail: () => (/* binding */ isValidEmail),\n/* harmony export */   sanitizeInput: () => (/* binding */ sanitizeInput),\n/* harmony export */   secureApiRequest: () => (/* binding */ secureApiRequest),\n/* harmony export */   secureLogin: () => (/* binding */ secureLogin),\n/* harmony export */   secureLogout: () => (/* binding */ secureLogout),\n/* harmony export */   validateCSRFToken: () => (/* binding */ validateCSRFToken),\n/* harmony export */   validatePassword: () => (/* binding */ validatePassword)\n/* harmony export */ });\n/**\n * Secure authentication utilities for client-side use\n * This replaces the vulnerable localStorage token storage\n */ // CSRF token management\nlet csrfToken = null;\n/**\n * Generate a random CSRF token\n */ function generateCSRFToken() {\n    const array = new Uint8Array(32);\n    crypto.getRandomValues(array);\n    return Array.from(array, (byte)=>byte.toString(16).padStart(2, \"0\")).join(\"\");\n}\n/**\n * Get CSRF token from server or generate new one\n */ async function getCSRFToken() {\n    // In development, use a simple bypass token\n    if (true) {\n        return \"dev-bypass\";\n    }\n    if (!csrfToken) {\n        try {\n            // Try to get CSRF token from server\n            const response = await fetch(\"/api/auth/csrf-token\", {\n                method: \"GET\",\n                credentials: \"include\"\n            });\n            if (response.ok) {\n                const data = await response.json();\n                csrfToken = data.token;\n            } else {\n                // Fallback: generate client-side token\n                csrfToken = generateCSRFToken();\n            }\n        } catch (error) {\n            // Fallback: generate client-side token\n            csrfToken = generateCSRFToken();\n        }\n    }\n    return csrfToken;\n}\n/**\n * Validate CSRF token (server-side validation)\n */ function validateCSRFToken(token) {\n    return token === csrfToken;\n}\n/**\n * These functions are moved to server-side utilities\n * Client-side code should use the secure API endpoints instead\n */ /**\n * Client-side authentication status check\n * This uses a secure API endpoint instead of direct token access\n */ async function checkAuthStatus() {\n    try {\n        const response = await fetch(\"/api/auth/status\", {\n            method: \"GET\",\n            credentials: \"include\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        if (response.ok) {\n            const data = await response.json();\n            return {\n                isAuthenticated: true,\n                user: data.user\n            };\n        } else {\n            return {\n                isAuthenticated: false,\n                error: \"Authentication check failed\"\n            };\n        }\n    } catch (error) {\n        console.error(\"Auth status check error:\", error);\n        return {\n            isAuthenticated: false,\n            error: \"Network error during authentication check\"\n        };\n    }\n}\n/**\n * Secure login function\n */ async function secureLogin(email, password) {\n    try {\n        const csrfToken = await getCSRFToken();\n        const response = await fetch(\"/api/auth/login\", {\n            method: \"POST\",\n            credentials: \"include\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                \"X-CSRF-Token\": csrfToken\n            },\n            body: JSON.stringify({\n                email,\n                password\n            })\n        });\n        const data = await response.json();\n        if (response.ok) {\n            return {\n                success: true,\n                user: data.user\n            };\n        } else {\n            return {\n                success: false,\n                error: data.message || \"Login failed\"\n            };\n        }\n    } catch (error) {\n        console.error(\"Login error:\", error);\n        return {\n            success: false,\n            error: \"Network error during login\"\n        };\n    }\n}\n/**\n * Secure logout function\n */ async function secureLogout() {\n    try {\n        const csrfToken = await getCSRFToken();\n        const response = await fetch(\"/api/auth/logout\", {\n            method: \"POST\",\n            credentials: \"include\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                \"X-CSRF-Token\": csrfToken\n            }\n        });\n        if (response.ok) {\n            return {\n                success: true\n            };\n        } else {\n            const data = await response.json();\n            return {\n                success: false,\n                error: data.message || \"Logout failed\"\n            };\n        }\n    } catch (error) {\n        console.error(\"Logout error:\", error);\n        return {\n            success: false,\n            error: \"Network error during logout\"\n        };\n    }\n}\n/**\n * Secure API request wrapper\n */ async function secureApiRequest(url, options = {}) {\n    const csrfToken = await getCSRFToken();\n    const defaultHeaders = {\n        \"Content-Type\": \"application/json\",\n        \"X-CSRF-Token\": csrfToken\n    };\n    const mergedOptions = {\n        ...options,\n        credentials: \"include\",\n        headers: {\n            ...defaultHeaders,\n            ...options.headers\n        }\n    };\n    return fetch(url, mergedOptions);\n}\n/**\n * Input sanitization for XSS prevention\n */ function sanitizeInput(input) {\n    const div = document.createElement(\"div\");\n    div.textContent = input;\n    return div.innerHTML;\n}\n/**\n * Validate email format\n */ function isValidEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n}\n/**\n * Validate password strength\n */ function validatePassword(password) {\n    const errors = [];\n    if (password.length < 8) {\n        errors.push(\"Password must be at least 8 characters long\");\n    }\n    if (!/[A-Z]/.test(password)) {\n        errors.push(\"Password must contain at least one uppercase letter\");\n    }\n    if (!/[a-z]/.test(password)) {\n        errors.push(\"Password must contain at least one lowercase letter\");\n    }\n    if (!/\\d/.test(password)) {\n        errors.push(\"Password must contain at least one number\");\n    }\n    if (!/[!@#$%^&*(),.?\":{}|<>]/.test(password)) {\n        errors.push(\"Password must contain at least one special character\");\n    }\n    return {\n        isValid: errors.length === 0,\n        errors\n    };\n}\n/**\n * Rate limiting for client-side requests\n */ class RateLimiter {\n    constructor(maxRequests = 5, windowMs = 60000){\n        this.requests = new Map();\n        this.maxRequests = maxRequests;\n        this.windowMs = windowMs;\n    }\n    isAllowed(key) {\n        const now = Date.now();\n        const requests = this.requests.get(key) || [];\n        // Remove old requests outside the window\n        const validRequests = requests.filter((time)=>now - time < this.windowMs);\n        if (validRequests.length >= this.maxRequests) {\n            return false;\n        }\n        validRequests.push(now);\n        this.requests.set(key, validRequests);\n        return true;\n    }\n}\nconst authRateLimiter = new RateLimiter(5, 60000); // 5 requests per minute\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvc2VjdXJlLWF1dGgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFBQTs7O0NBR0MsR0FFRCx3QkFBd0I7QUFDeEIsSUFBSUEsWUFBMkI7QUFFL0I7O0NBRUMsR0FDTSxTQUFTQztJQUNkLE1BQU1DLFFBQVEsSUFBSUMsV0FBVztJQUM3QkMsT0FBT0MsZUFBZSxDQUFDSDtJQUN2QixPQUFPSSxNQUFNQyxJQUFJLENBQUNMLE9BQU9NLENBQUFBLE9BQVFBLEtBQUtDLFFBQVEsQ0FBQyxJQUFJQyxRQUFRLENBQUMsR0FBRyxNQUFNQyxJQUFJLENBQUM7QUFDNUU7QUFFQTs7Q0FFQyxHQUNNLGVBQWVDO0lBQ3BCLDRDQUE0QztJQUM1QyxJQUFJQyxJQUF5QixFQUFlO1FBQzFDLE9BQU87SUFDVDtJQUVBLElBQUksQ0FBQ2IsV0FBVztRQUNkLElBQUk7WUFDRixvQ0FBb0M7WUFDcEMsTUFBTWMsV0FBVyxNQUFNQyxNQUFNLHdCQUF3QjtnQkFDbkRDLFFBQVE7Z0JBQ1JDLGFBQWE7WUFDZjtZQUVBLElBQUlILFNBQVNJLEVBQUUsRUFBRTtnQkFDZixNQUFNQyxPQUFPLE1BQU1MLFNBQVNNLElBQUk7Z0JBQ2hDcEIsWUFBWW1CLEtBQUtFLEtBQUs7WUFDeEIsT0FBTztnQkFDTCx1Q0FBdUM7Z0JBQ3ZDckIsWUFBWUM7WUFDZDtRQUNGLEVBQUUsT0FBT3FCLE9BQU87WUFDZCx1Q0FBdUM7WUFDdkN0QixZQUFZQztRQUNkO0lBQ0Y7SUFDQSxPQUFPRDtBQUNUO0FBRUE7O0NBRUMsR0FDTSxTQUFTdUIsa0JBQWtCRixLQUFhO0lBQzdDLE9BQU9BLFVBQVVyQjtBQUNuQjtBQUVBOzs7Q0FHQyxHQUVEOzs7Q0FHQyxHQUNNLGVBQWV3QjtJQUtwQixJQUFJO1FBQ0YsTUFBTVYsV0FBVyxNQUFNQyxNQUFNLG9CQUFvQjtZQUMvQ0MsUUFBUTtZQUNSQyxhQUFhO1lBQ2JRLFNBQVM7Z0JBQ1AsZ0JBQWdCO1lBQ2xCO1FBQ0Y7UUFFQSxJQUFJWCxTQUFTSSxFQUFFLEVBQUU7WUFDZixNQUFNQyxPQUFPLE1BQU1MLFNBQVNNLElBQUk7WUFDaEMsT0FBTztnQkFDTE0saUJBQWlCO2dCQUNqQkMsTUFBTVIsS0FBS1EsSUFBSTtZQUNqQjtRQUNGLE9BQU87WUFDTCxPQUFPO2dCQUNMRCxpQkFBaUI7Z0JBQ2pCSixPQUFPO1lBQ1Q7UUFDRjtJQUNGLEVBQUUsT0FBT0EsT0FBTztRQUNkTSxRQUFRTixLQUFLLENBQUMsNEJBQTRCQTtRQUMxQyxPQUFPO1lBQ0xJLGlCQUFpQjtZQUNqQkosT0FBTztRQUNUO0lBQ0Y7QUFDRjtBQUVBOztDQUVDLEdBQ00sZUFBZU8sWUFBWUMsS0FBYSxFQUFFQyxRQUFnQjtJQUsvRCxJQUFJO1FBQ0YsTUFBTS9CLFlBQVksTUFBTVk7UUFFeEIsTUFBTUUsV0FBVyxNQUFNQyxNQUFNLG1CQUFtQjtZQUM5Q0MsUUFBUTtZQUNSQyxhQUFhO1lBQ2JRLFNBQVM7Z0JBQ1AsZ0JBQWdCO2dCQUNoQixnQkFBZ0J6QjtZQUNsQjtZQUNBZ0MsTUFBTUMsS0FBS0MsU0FBUyxDQUFDO2dCQUFFSjtnQkFBT0M7WUFBUztRQUN6QztRQUVBLE1BQU1aLE9BQU8sTUFBTUwsU0FBU00sSUFBSTtRQUVoQyxJQUFJTixTQUFTSSxFQUFFLEVBQUU7WUFDZixPQUFPO2dCQUNMaUIsU0FBUztnQkFDVFIsTUFBTVIsS0FBS1EsSUFBSTtZQUNqQjtRQUNGLE9BQU87WUFDTCxPQUFPO2dCQUNMUSxTQUFTO2dCQUNUYixPQUFPSCxLQUFLaUIsT0FBTyxJQUFJO1lBQ3pCO1FBQ0Y7SUFDRixFQUFFLE9BQU9kLE9BQU87UUFDZE0sUUFBUU4sS0FBSyxDQUFDLGdCQUFnQkE7UUFDOUIsT0FBTztZQUNMYSxTQUFTO1lBQ1RiLE9BQU87UUFDVDtJQUNGO0FBQ0Y7QUFFQTs7Q0FFQyxHQUNNLGVBQWVlO0lBSXBCLElBQUk7UUFDRixNQUFNckMsWUFBWSxNQUFNWTtRQUV4QixNQUFNRSxXQUFXLE1BQU1DLE1BQU0sb0JBQW9CO1lBQy9DQyxRQUFRO1lBQ1JDLGFBQWE7WUFDYlEsU0FBUztnQkFDUCxnQkFBZ0I7Z0JBQ2hCLGdCQUFnQnpCO1lBQ2xCO1FBQ0Y7UUFFQSxJQUFJYyxTQUFTSSxFQUFFLEVBQUU7WUFDZixPQUFPO2dCQUFFaUIsU0FBUztZQUFLO1FBQ3pCLE9BQU87WUFDTCxNQUFNaEIsT0FBTyxNQUFNTCxTQUFTTSxJQUFJO1lBQ2hDLE9BQU87Z0JBQ0xlLFNBQVM7Z0JBQ1RiLE9BQU9ILEtBQUtpQixPQUFPLElBQUk7WUFDekI7UUFDRjtJQUNGLEVBQUUsT0FBT2QsT0FBTztRQUNkTSxRQUFRTixLQUFLLENBQUMsaUJBQWlCQTtRQUMvQixPQUFPO1lBQ0xhLFNBQVM7WUFDVGIsT0FBTztRQUNUO0lBQ0Y7QUFDRjtBQUVBOztDQUVDLEdBQ00sZUFBZWdCLGlCQUNwQkMsR0FBVyxFQUNYQyxVQUF1QixDQUFDLENBQUM7SUFFekIsTUFBTXhDLFlBQVksTUFBTVk7SUFFeEIsTUFBTTZCLGlCQUFpQjtRQUNyQixnQkFBZ0I7UUFDaEIsZ0JBQWdCekM7SUFDbEI7SUFFQSxNQUFNMEMsZ0JBQTZCO1FBQ2pDLEdBQUdGLE9BQU87UUFDVnZCLGFBQWE7UUFDYlEsU0FBUztZQUNQLEdBQUdnQixjQUFjO1lBQ2pCLEdBQUdELFFBQVFmLE9BQU87UUFDcEI7SUFDRjtJQUVBLE9BQU9WLE1BQU13QixLQUFLRztBQUNwQjtBQUVBOztDQUVDLEdBQ00sU0FBU0MsY0FBY0MsS0FBYTtJQUN6QyxNQUFNQyxNQUFNQyxTQUFTQyxhQUFhLENBQUM7SUFDbkNGLElBQUlHLFdBQVcsR0FBR0o7SUFDbEIsT0FBT0MsSUFBSUksU0FBUztBQUN0QjtBQUVBOztDQUVDLEdBQ00sU0FBU0MsYUFBYXBCLEtBQWE7SUFDeEMsTUFBTXFCLGFBQWE7SUFDbkIsT0FBT0EsV0FBV0MsSUFBSSxDQUFDdEI7QUFDekI7QUFFQTs7Q0FFQyxHQUNNLFNBQVN1QixpQkFBaUJ0QixRQUFnQjtJQUkvQyxNQUFNdUIsU0FBbUIsRUFBRTtJQUUzQixJQUFJdkIsU0FBU3dCLE1BQU0sR0FBRyxHQUFHO1FBQ3ZCRCxPQUFPRSxJQUFJLENBQUM7SUFDZDtJQUVBLElBQUksQ0FBQyxRQUFRSixJQUFJLENBQUNyQixXQUFXO1FBQzNCdUIsT0FBT0UsSUFBSSxDQUFDO0lBQ2Q7SUFFQSxJQUFJLENBQUMsUUFBUUosSUFBSSxDQUFDckIsV0FBVztRQUMzQnVCLE9BQU9FLElBQUksQ0FBQztJQUNkO0lBRUEsSUFBSSxDQUFDLEtBQUtKLElBQUksQ0FBQ3JCLFdBQVc7UUFDeEJ1QixPQUFPRSxJQUFJLENBQUM7SUFDZDtJQUVBLElBQUksQ0FBQyx5QkFBeUJKLElBQUksQ0FBQ3JCLFdBQVc7UUFDNUN1QixPQUFPRSxJQUFJLENBQUM7SUFDZDtJQUVBLE9BQU87UUFDTEMsU0FBU0gsT0FBT0MsTUFBTSxLQUFLO1FBQzNCRDtJQUNGO0FBQ0Y7QUFFQTs7Q0FFQyxHQUNELE1BQU1JO0lBS0pDLFlBQVlDLGNBQXNCLENBQUMsRUFBRUMsV0FBbUIsS0FBSyxDQUFFO2FBSnZEQyxXQUFrQyxJQUFJQztRQUs1QyxJQUFJLENBQUNILFdBQVcsR0FBR0E7UUFDbkIsSUFBSSxDQUFDQyxRQUFRLEdBQUdBO0lBQ2xCO0lBRUFHLFVBQVVDLEdBQVcsRUFBVztRQUM5QixNQUFNQyxNQUFNQyxLQUFLRCxHQUFHO1FBQ3BCLE1BQU1KLFdBQVcsSUFBSSxDQUFDQSxRQUFRLENBQUNNLEdBQUcsQ0FBQ0gsUUFBUSxFQUFFO1FBRTdDLHlDQUF5QztRQUN6QyxNQUFNSSxnQkFBZ0JQLFNBQVNRLE1BQU0sQ0FBQ0MsQ0FBQUEsT0FBUUwsTUFBTUssT0FBTyxJQUFJLENBQUNWLFFBQVE7UUFFeEUsSUFBSVEsY0FBY2QsTUFBTSxJQUFJLElBQUksQ0FBQ0ssV0FBVyxFQUFFO1lBQzVDLE9BQU87UUFDVDtRQUVBUyxjQUFjYixJQUFJLENBQUNVO1FBQ25CLElBQUksQ0FBQ0osUUFBUSxDQUFDVSxHQUFHLENBQUNQLEtBQUtJO1FBQ3ZCLE9BQU87SUFDVDtBQUNGO0FBRU8sTUFBTUksa0JBQWtCLElBQUlmLFlBQVksR0FBRyxPQUFPLENBQUMsd0JBQXdCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbXktdjAtcHJvamVjdC8uL2xpYi9zZWN1cmUtYXV0aC50cz9lMDM1Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogU2VjdXJlIGF1dGhlbnRpY2F0aW9uIHV0aWxpdGllcyBmb3IgY2xpZW50LXNpZGUgdXNlXG4gKiBUaGlzIHJlcGxhY2VzIHRoZSB2dWxuZXJhYmxlIGxvY2FsU3RvcmFnZSB0b2tlbiBzdG9yYWdlXG4gKi9cblxuLy8gQ1NSRiB0b2tlbiBtYW5hZ2VtZW50XG5sZXQgY3NyZlRva2VuOiBzdHJpbmcgfCBudWxsID0gbnVsbDtcblxuLyoqXG4gKiBHZW5lcmF0ZSBhIHJhbmRvbSBDU1JGIHRva2VuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBnZW5lcmF0ZUNTUkZUb2tlbigpOiBzdHJpbmcge1xuICBjb25zdCBhcnJheSA9IG5ldyBVaW50OEFycmF5KDMyKTtcbiAgY3J5cHRvLmdldFJhbmRvbVZhbHVlcyhhcnJheSk7XG4gIHJldHVybiBBcnJheS5mcm9tKGFycmF5LCBieXRlID0+IGJ5dGUudG9TdHJpbmcoMTYpLnBhZFN0YXJ0KDIsICcwJykpLmpvaW4oJycpO1xufVxuXG4vKipcbiAqIEdldCBDU1JGIHRva2VuIGZyb20gc2VydmVyIG9yIGdlbmVyYXRlIG5ldyBvbmVcbiAqL1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGdldENTUkZUb2tlbigpOiBQcm9taXNlPHN0cmluZz4ge1xuICAvLyBJbiBkZXZlbG9wbWVudCwgdXNlIGEgc2ltcGxlIGJ5cGFzcyB0b2tlblxuICBpZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdkZXZlbG9wbWVudCcpIHtcbiAgICByZXR1cm4gJ2Rldi1ieXBhc3MnO1xuICB9XG5cbiAgaWYgKCFjc3JmVG9rZW4pIHtcbiAgICB0cnkge1xuICAgICAgLy8gVHJ5IHRvIGdldCBDU1JGIHRva2VuIGZyb20gc2VydmVyXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL2F1dGgvY3NyZi10b2tlbicsIHtcbiAgICAgICAgbWV0aG9kOiAnR0VUJyxcbiAgICAgICAgY3JlZGVudGlhbHM6ICdpbmNsdWRlJyxcbiAgICAgIH0pO1xuXG4gICAgICBpZiAocmVzcG9uc2Uub2spIHtcbiAgICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgICAgY3NyZlRva2VuID0gZGF0YS50b2tlbjtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIC8vIEZhbGxiYWNrOiBnZW5lcmF0ZSBjbGllbnQtc2lkZSB0b2tlblxuICAgICAgICBjc3JmVG9rZW4gPSBnZW5lcmF0ZUNTUkZUb2tlbigpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAvLyBGYWxsYmFjazogZ2VuZXJhdGUgY2xpZW50LXNpZGUgdG9rZW5cbiAgICAgIGNzcmZUb2tlbiA9IGdlbmVyYXRlQ1NSRlRva2VuKCk7XG4gICAgfVxuICB9XG4gIHJldHVybiBjc3JmVG9rZW47XG59XG5cbi8qKlxuICogVmFsaWRhdGUgQ1NSRiB0b2tlbiAoc2VydmVyLXNpZGUgdmFsaWRhdGlvbilcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHZhbGlkYXRlQ1NSRlRva2VuKHRva2VuOiBzdHJpbmcpOiBib29sZWFuIHtcbiAgcmV0dXJuIHRva2VuID09PSBjc3JmVG9rZW47XG59XG5cbi8qKlxuICogVGhlc2UgZnVuY3Rpb25zIGFyZSBtb3ZlZCB0byBzZXJ2ZXItc2lkZSB1dGlsaXRpZXNcbiAqIENsaWVudC1zaWRlIGNvZGUgc2hvdWxkIHVzZSB0aGUgc2VjdXJlIEFQSSBlbmRwb2ludHMgaW5zdGVhZFxuICovXG5cbi8qKlxuICogQ2xpZW50LXNpZGUgYXV0aGVudGljYXRpb24gc3RhdHVzIGNoZWNrXG4gKiBUaGlzIHVzZXMgYSBzZWN1cmUgQVBJIGVuZHBvaW50IGluc3RlYWQgb2YgZGlyZWN0IHRva2VuIGFjY2Vzc1xuICovXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gY2hlY2tBdXRoU3RhdHVzKCk6IFByb21pc2U8e1xuICBpc0F1dGhlbnRpY2F0ZWQ6IGJvb2xlYW47XG4gIHVzZXI/OiBhbnk7XG4gIGVycm9yPzogc3RyaW5nO1xufT4ge1xuICB0cnkge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvYXV0aC9zdGF0dXMnLCB7XG4gICAgICBtZXRob2Q6ICdHRVQnLFxuICAgICAgY3JlZGVudGlhbHM6ICdpbmNsdWRlJywgLy8gSW5jbHVkZSBjb29raWVzXG4gICAgICBoZWFkZXJzOiB7XG4gICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICB9LFxuICAgIH0pO1xuXG4gICAgaWYgKHJlc3BvbnNlLm9rKSB7XG4gICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgcmV0dXJuIHtcbiAgICAgICAgaXNBdXRoZW50aWNhdGVkOiB0cnVlLFxuICAgICAgICB1c2VyOiBkYXRhLnVzZXIsXG4gICAgICB9O1xuICAgIH0gZWxzZSB7XG4gICAgICByZXR1cm4ge1xuICAgICAgICBpc0F1dGhlbnRpY2F0ZWQ6IGZhbHNlLFxuICAgICAgICBlcnJvcjogJ0F1dGhlbnRpY2F0aW9uIGNoZWNrIGZhaWxlZCcsXG4gICAgICB9O1xuICAgIH1cbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdBdXRoIHN0YXR1cyBjaGVjayBlcnJvcjonLCBlcnJvcik7XG4gICAgcmV0dXJuIHtcbiAgICAgIGlzQXV0aGVudGljYXRlZDogZmFsc2UsXG4gICAgICBlcnJvcjogJ05ldHdvcmsgZXJyb3IgZHVyaW5nIGF1dGhlbnRpY2F0aW9uIGNoZWNrJyxcbiAgICB9O1xuICB9XG59XG5cbi8qKlxuICogU2VjdXJlIGxvZ2luIGZ1bmN0aW9uXG4gKi9cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBzZWN1cmVMb2dpbihlbWFpbDogc3RyaW5nLCBwYXNzd29yZDogc3RyaW5nKTogUHJvbWlzZTx7XG4gIHN1Y2Nlc3M6IGJvb2xlYW47XG4gIHVzZXI/OiBhbnk7XG4gIGVycm9yPzogc3RyaW5nO1xufT4ge1xuICB0cnkge1xuICAgIGNvbnN0IGNzcmZUb2tlbiA9IGF3YWl0IGdldENTUkZUb2tlbigpO1xuXG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS9hdXRoL2xvZ2luJywge1xuICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICBjcmVkZW50aWFsczogJ2luY2x1ZGUnLFxuICAgICAgaGVhZGVyczoge1xuICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICAnWC1DU1JGLVRva2VuJzogY3NyZlRva2VuLFxuICAgICAgfSxcbiAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHsgZW1haWwsIHBhc3N3b3JkIH0pLFxuICAgIH0pO1xuXG4gICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcblxuICAgIGlmIChyZXNwb25zZS5vaykge1xuICAgICAgcmV0dXJuIHtcbiAgICAgICAgc3VjY2VzczogdHJ1ZSxcbiAgICAgICAgdXNlcjogZGF0YS51c2VyLFxuICAgICAgfTtcbiAgICB9IGVsc2Uge1xuICAgICAgcmV0dXJuIHtcbiAgICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICAgIGVycm9yOiBkYXRhLm1lc3NhZ2UgfHwgJ0xvZ2luIGZhaWxlZCcsXG4gICAgICB9O1xuICAgIH1cbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdMb2dpbiBlcnJvcjonLCBlcnJvcik7XG4gICAgcmV0dXJuIHtcbiAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgZXJyb3I6ICdOZXR3b3JrIGVycm9yIGR1cmluZyBsb2dpbicsXG4gICAgfTtcbiAgfVxufVxuXG4vKipcbiAqIFNlY3VyZSBsb2dvdXQgZnVuY3Rpb25cbiAqL1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIHNlY3VyZUxvZ291dCgpOiBQcm9taXNlPHtcbiAgc3VjY2VzczogYm9vbGVhbjtcbiAgZXJyb3I/OiBzdHJpbmc7XG59PiB7XG4gIHRyeSB7XG4gICAgY29uc3QgY3NyZlRva2VuID0gYXdhaXQgZ2V0Q1NSRlRva2VuKCk7XG5cbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL2F1dGgvbG9nb3V0Jywge1xuICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICBjcmVkZW50aWFsczogJ2luY2x1ZGUnLFxuICAgICAgaGVhZGVyczoge1xuICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICAnWC1DU1JGLVRva2VuJzogY3NyZlRva2VuLFxuICAgICAgfSxcbiAgICB9KTtcblxuICAgIGlmIChyZXNwb25zZS5vaykge1xuICAgICAgcmV0dXJuIHsgc3VjY2VzczogdHJ1ZSB9O1xuICAgIH0gZWxzZSB7XG4gICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgcmV0dXJuIHtcbiAgICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICAgIGVycm9yOiBkYXRhLm1lc3NhZ2UgfHwgJ0xvZ291dCBmYWlsZWQnLFxuICAgICAgfTtcbiAgICB9XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignTG9nb3V0IGVycm9yOicsIGVycm9yKTtcbiAgICByZXR1cm4ge1xuICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICBlcnJvcjogJ05ldHdvcmsgZXJyb3IgZHVyaW5nIGxvZ291dCcsXG4gICAgfTtcbiAgfVxufVxuXG4vKipcbiAqIFNlY3VyZSBBUEkgcmVxdWVzdCB3cmFwcGVyXG4gKi9cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBzZWN1cmVBcGlSZXF1ZXN0KFxuICB1cmw6IHN0cmluZyxcbiAgb3B0aW9uczogUmVxdWVzdEluaXQgPSB7fVxuKTogUHJvbWlzZTxSZXNwb25zZT4ge1xuICBjb25zdCBjc3JmVG9rZW4gPSBhd2FpdCBnZXRDU1JGVG9rZW4oKTtcblxuICBjb25zdCBkZWZhdWx0SGVhZGVycyA9IHtcbiAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICdYLUNTUkYtVG9rZW4nOiBjc3JmVG9rZW4sXG4gIH07XG5cbiAgY29uc3QgbWVyZ2VkT3B0aW9uczogUmVxdWVzdEluaXQgPSB7XG4gICAgLi4ub3B0aW9ucyxcbiAgICBjcmVkZW50aWFsczogJ2luY2x1ZGUnLFxuICAgIGhlYWRlcnM6IHtcbiAgICAgIC4uLmRlZmF1bHRIZWFkZXJzLFxuICAgICAgLi4ub3B0aW9ucy5oZWFkZXJzLFxuICAgIH0sXG4gIH07XG5cbiAgcmV0dXJuIGZldGNoKHVybCwgbWVyZ2VkT3B0aW9ucyk7XG59XG5cbi8qKlxuICogSW5wdXQgc2FuaXRpemF0aW9uIGZvciBYU1MgcHJldmVudGlvblxuICovXG5leHBvcnQgZnVuY3Rpb24gc2FuaXRpemVJbnB1dChpbnB1dDogc3RyaW5nKTogc3RyaW5nIHtcbiAgY29uc3QgZGl2ID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnZGl2Jyk7XG4gIGRpdi50ZXh0Q29udGVudCA9IGlucHV0O1xuICByZXR1cm4gZGl2LmlubmVySFRNTDtcbn1cblxuLyoqXG4gKiBWYWxpZGF0ZSBlbWFpbCBmb3JtYXRcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGlzVmFsaWRFbWFpbChlbWFpbDogc3RyaW5nKTogYm9vbGVhbiB7XG4gIGNvbnN0IGVtYWlsUmVnZXggPSAvXlteXFxzQF0rQFteXFxzQF0rXFwuW15cXHNAXSskLztcbiAgcmV0dXJuIGVtYWlsUmVnZXgudGVzdChlbWFpbCk7XG59XG5cbi8qKlxuICogVmFsaWRhdGUgcGFzc3dvcmQgc3RyZW5ndGhcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHZhbGlkYXRlUGFzc3dvcmQocGFzc3dvcmQ6IHN0cmluZyk6IHtcbiAgaXNWYWxpZDogYm9vbGVhbjtcbiAgZXJyb3JzOiBzdHJpbmdbXTtcbn0ge1xuICBjb25zdCBlcnJvcnM6IHN0cmluZ1tdID0gW107XG4gIFxuICBpZiAocGFzc3dvcmQubGVuZ3RoIDwgOCkge1xuICAgIGVycm9ycy5wdXNoKCdQYXNzd29yZCBtdXN0IGJlIGF0IGxlYXN0IDggY2hhcmFjdGVycyBsb25nJyk7XG4gIH1cbiAgXG4gIGlmICghL1tBLVpdLy50ZXN0KHBhc3N3b3JkKSkge1xuICAgIGVycm9ycy5wdXNoKCdQYXNzd29yZCBtdXN0IGNvbnRhaW4gYXQgbGVhc3Qgb25lIHVwcGVyY2FzZSBsZXR0ZXInKTtcbiAgfVxuICBcbiAgaWYgKCEvW2Etel0vLnRlc3QocGFzc3dvcmQpKSB7XG4gICAgZXJyb3JzLnB1c2goJ1Bhc3N3b3JkIG11c3QgY29udGFpbiBhdCBsZWFzdCBvbmUgbG93ZXJjYXNlIGxldHRlcicpO1xuICB9XG4gIFxuICBpZiAoIS9cXGQvLnRlc3QocGFzc3dvcmQpKSB7XG4gICAgZXJyb3JzLnB1c2goJ1Bhc3N3b3JkIG11c3QgY29udGFpbiBhdCBsZWFzdCBvbmUgbnVtYmVyJyk7XG4gIH1cbiAgXG4gIGlmICghL1shQCMkJV4mKigpLC4/XCI6e318PD5dLy50ZXN0KHBhc3N3b3JkKSkge1xuICAgIGVycm9ycy5wdXNoKCdQYXNzd29yZCBtdXN0IGNvbnRhaW4gYXQgbGVhc3Qgb25lIHNwZWNpYWwgY2hhcmFjdGVyJyk7XG4gIH1cbiAgXG4gIHJldHVybiB7XG4gICAgaXNWYWxpZDogZXJyb3JzLmxlbmd0aCA9PT0gMCxcbiAgICBlcnJvcnMsXG4gIH07XG59XG5cbi8qKlxuICogUmF0ZSBsaW1pdGluZyBmb3IgY2xpZW50LXNpZGUgcmVxdWVzdHNcbiAqL1xuY2xhc3MgUmF0ZUxpbWl0ZXIge1xuICBwcml2YXRlIHJlcXVlc3RzOiBNYXA8c3RyaW5nLCBudW1iZXJbXT4gPSBuZXcgTWFwKCk7XG4gIHByaXZhdGUgcmVhZG9ubHkgbWF4UmVxdWVzdHM6IG51bWJlcjtcbiAgcHJpdmF0ZSByZWFkb25seSB3aW5kb3dNczogbnVtYmVyO1xuXG4gIGNvbnN0cnVjdG9yKG1heFJlcXVlc3RzOiBudW1iZXIgPSA1LCB3aW5kb3dNczogbnVtYmVyID0gNjAwMDApIHtcbiAgICB0aGlzLm1heFJlcXVlc3RzID0gbWF4UmVxdWVzdHM7XG4gICAgdGhpcy53aW5kb3dNcyA9IHdpbmRvd01zO1xuICB9XG5cbiAgaXNBbGxvd2VkKGtleTogc3RyaW5nKTogYm9vbGVhbiB7XG4gICAgY29uc3Qgbm93ID0gRGF0ZS5ub3coKTtcbiAgICBjb25zdCByZXF1ZXN0cyA9IHRoaXMucmVxdWVzdHMuZ2V0KGtleSkgfHwgW107XG4gICAgXG4gICAgLy8gUmVtb3ZlIG9sZCByZXF1ZXN0cyBvdXRzaWRlIHRoZSB3aW5kb3dcbiAgICBjb25zdCB2YWxpZFJlcXVlc3RzID0gcmVxdWVzdHMuZmlsdGVyKHRpbWUgPT4gbm93IC0gdGltZSA8IHRoaXMud2luZG93TXMpO1xuICAgIFxuICAgIGlmICh2YWxpZFJlcXVlc3RzLmxlbmd0aCA+PSB0aGlzLm1heFJlcXVlc3RzKSB7XG4gICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICAgIFxuICAgIHZhbGlkUmVxdWVzdHMucHVzaChub3cpO1xuICAgIHRoaXMucmVxdWVzdHMuc2V0KGtleSwgdmFsaWRSZXF1ZXN0cyk7XG4gICAgcmV0dXJuIHRydWU7XG4gIH1cbn1cblxuZXhwb3J0IGNvbnN0IGF1dGhSYXRlTGltaXRlciA9IG5ldyBSYXRlTGltaXRlcig1LCA2MDAwMCk7IC8vIDUgcmVxdWVzdHMgcGVyIG1pbnV0ZVxuIl0sIm5hbWVzIjpbImNzcmZUb2tlbiIsImdlbmVyYXRlQ1NSRlRva2VuIiwiYXJyYXkiLCJVaW50OEFycmF5IiwiY3J5cHRvIiwiZ2V0UmFuZG9tVmFsdWVzIiwiQXJyYXkiLCJmcm9tIiwiYnl0ZSIsInRvU3RyaW5nIiwicGFkU3RhcnQiLCJqb2luIiwiZ2V0Q1NSRlRva2VuIiwicHJvY2VzcyIsInJlc3BvbnNlIiwiZmV0Y2giLCJtZXRob2QiLCJjcmVkZW50aWFscyIsIm9rIiwiZGF0YSIsImpzb24iLCJ0b2tlbiIsImVycm9yIiwidmFsaWRhdGVDU1JGVG9rZW4iLCJjaGVja0F1dGhTdGF0dXMiLCJoZWFkZXJzIiwiaXNBdXRoZW50aWNhdGVkIiwidXNlciIsImNvbnNvbGUiLCJzZWN1cmVMb2dpbiIsImVtYWlsIiwicGFzc3dvcmQiLCJib2R5IiwiSlNPTiIsInN0cmluZ2lmeSIsInN1Y2Nlc3MiLCJtZXNzYWdlIiwic2VjdXJlTG9nb3V0Iiwic2VjdXJlQXBpUmVxdWVzdCIsInVybCIsIm9wdGlvbnMiLCJkZWZhdWx0SGVhZGVycyIsIm1lcmdlZE9wdGlvbnMiLCJzYW5pdGl6ZUlucHV0IiwiaW5wdXQiLCJkaXYiLCJkb2N1bWVudCIsImNyZWF0ZUVsZW1lbnQiLCJ0ZXh0Q29udGVudCIsImlubmVySFRNTCIsImlzVmFsaWRFbWFpbCIsImVtYWlsUmVnZXgiLCJ0ZXN0IiwidmFsaWRhdGVQYXNzd29yZCIsImVycm9ycyIsImxlbmd0aCIsInB1c2giLCJpc1ZhbGlkIiwiUmF0ZUxpbWl0ZXIiLCJjb25zdHJ1Y3RvciIsIm1heFJlcXVlc3RzIiwid2luZG93TXMiLCJyZXF1ZXN0cyIsIk1hcCIsImlzQWxsb3dlZCIsImtleSIsIm5vdyIsIkRhdGUiLCJnZXQiLCJ2YWxpZFJlcXVlc3RzIiwiZmlsdGVyIiwidGltZSIsInNldCIsImF1dGhSYXRlTGltaXRlciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./lib/secure-auth.ts\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRDO0FBQ0o7QUFFakMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbXktdjAtcHJvamVjdC8uL2xpYi91dGlscy50cz9mNzQ1Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNsc3gsIHR5cGUgQ2xhc3NWYWx1ZSB9IGZyb20gXCJjbHN4XCJcbmltcG9ydCB7IHR3TWVyZ2UgfSBmcm9tIFwidGFpbHdpbmQtbWVyZ2VcIlxuXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKVxufVxuIl0sIm5hbWVzIjpbImNsc3giLCJ0d01lcmdlIiwiY24iLCJpbnB1dHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"51a7bf1146cf\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9teS12MC1wcm9qZWN0Ly4vYXBwL2dsb2JhbHMuY3NzPzhmYjEiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI1MWE3YmYxMTQ2Y2ZcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/(auth)/login/page.tsx":
/*!***********************************!*\
  !*** ./app/(auth)/login/page.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__),
/* harmony export */   dynamic: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Dev Ideas\CareerPilotAI-2\frontend\app\(auth)\login\page.tsx#dynamic`);
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Dev Ideas\CareerPilotAI-2\frontend\app\(auth)\login\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   dynamic: () => (/* binding */ dynamic),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   revalidate: () => (/* binding */ revalidate),\n/* harmony export */   runtime: () => (/* binding */ runtime)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _app_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/app/globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _components_theme_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/theme-provider */ \"(rsc)/./components/theme-provider.tsx\");\n/* harmony import */ var _lib_auth_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/auth-context */ \"(rsc)/./lib/auth-context.tsx\");\n/* harmony import */ var _components_ui_toaster__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/toaster */ \"(rsc)/./components/ui/toaster.tsx\");\n\n\n\n\n\n\n// Force dynamic rendering to avoid SSG issues\nconst dynamic = \"force-dynamic\";\nconst runtime = \"nodejs\";\nconst revalidate = 0;\nconst metadata = {\n    title: \"CareerPilotAI\",\n    description: \"Generate professional resumes, cover letters, and LinkedIn bios\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default().className)} antialiased transition-colors duration-300`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_auth_context__WEBPACK_IMPORTED_MODULE_3__.AuthProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_theme_provider__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-fade-in [animation-delay:100ms] transition-all duration-300\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Dev Ideas\\\\CareerPilotAI-2\\\\frontend\\\\app\\\\layout.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toaster__WEBPACK_IMPORTED_MODULE_4__.Toaster, {}, void 0, false, {\n                            fileName: \"D:\\\\Dev Ideas\\\\CareerPilotAI-2\\\\frontend\\\\app\\\\layout.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Dev Ideas\\\\CareerPilotAI-2\\\\frontend\\\\app\\\\layout.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Dev Ideas\\\\CareerPilotAI-2\\\\frontend\\\\app\\\\layout.tsx\",\n                lineNumber: 29,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Dev Ideas\\\\CareerPilotAI-2\\\\frontend\\\\app\\\\layout.tsx\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Dev Ideas\\\\CareerPilotAI-2\\\\frontend\\\\app\\\\layout.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/not-found.tsx":
/*!***************************!*\
  !*** ./app/not-found.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__),
/* harmony export */   dynamic: () => (/* binding */ e0),
/* harmony export */   revalidate: () => (/* binding */ e2),
/* harmony export */   runtime: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Dev Ideas\CareerPilotAI-2\frontend\app\not-found.tsx#dynamic`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Dev Ideas\CareerPilotAI-2\frontend\app\not-found.tsx#runtime`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Dev Ideas\CareerPilotAI-2\frontend\app\not-found.tsx#revalidate`);
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Dev Ideas\CareerPilotAI-2\frontend\app\not-found.tsx#default`));


/***/ }),

/***/ "(rsc)/./components/theme-provider.tsx":
/*!***************************************!*\
  !*** ./components/theme-provider.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Dev Ideas\CareerPilotAI-2\frontend\components\theme-provider.tsx#ThemeProvider`);


/***/ }),

/***/ "(rsc)/./components/ui/toaster.tsx":
/*!***********************************!*\
  !*** ./components/ui/toaster.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Toaster: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Dev Ideas\CareerPilotAI-2\frontend\components\ui\toaster.tsx#Toaster`);


/***/ }),

/***/ "(rsc)/./lib/auth-context.tsx":
/*!******************************!*\
  !*** ./lib/auth-context.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ e0),
/* harmony export */   useAuth: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Dev Ideas\CareerPilotAI-2\frontend\lib\auth-context.tsx#AuthProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Dev Ideas\CareerPilotAI-2\frontend\lib\auth-context.tsx#useAuth`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@radix-ui","vendor-chunks/tailwind-merge","vendor-chunks/lucide-react","vendor-chunks/class-variance-authority","vendor-chunks/next-themes","vendor-chunks/@swc","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F(auth)%2Flogin%2Fpage&page=%2F(auth)%2Flogin%2Fpage&appPaths=%2F(auth)%2Flogin%2Fpage&pagePath=private-next-app-dir%2F(auth)%2Flogin%2Fpage.tsx&appDir=D%3A%5CDev%20Ideas%5CCareerPilotAI-2%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CDev%20Ideas%5CCareerPilotAI-2%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();