"use client";

import type React from "react";
import { createContext, useContext, useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { secureAuthAPI } from "@/lib/secure-api";
import type { AuthState } from "@/lib/types";
import { useToast } from "@/hooks/use-toast";
import {
  authRateLimiter,
  isValidEmail,
  validatePassword,
} from "@/lib/secure-auth";

interface AuthContextType extends AuthState {
  login: (email: string, password: string) => Promise<void>;
  register: (name: string, email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    isAuthenticated: false,
    loading: true,
    error: null,
  });

  const router = useRouter();
  const { toast } = useToast();

  // Load user on initial render using secure authentication
  useEffect(() => {
    const loadUser = async () => {
      try {
        const res = await secureAuthAPI.getCurrentUser();
        if (res.success && res.isAuthenticated) {
          setAuthState({
            user: res.user,
            isAuthenticated: true,
            loading: false,
            error: null,
          });
        } else {
          // Not authenticated, but not an error
          setAuthState({
            user: null,
            isAuthenticated: false,
            loading: false,
            error: null,
          });
        }
      } catch (err: any) {
        console.error("Error loading user:", err);
        setAuthState({
          user: null,
          isAuthenticated: false,
          loading: false,
          error: null, // Don't show error for unauthenticated state
        });
      }
    };

    loadUser();
  }, []);

  // Register user with enhanced security
  const register = async (name: string, email: string, password: string) => {
    setAuthState((prev) => ({ ...prev, loading: true, error: null }));

    try {
      // Client-side validation
      if (!name.trim()) {
        throw new Error("Name is required");
      }

      if (!isValidEmail(email)) {
        throw new Error("Please enter a valid email address");
      }

      const passwordValidation = validatePassword(password);
      if (!passwordValidation.isValid) {
        throw new Error(passwordValidation.errors[0]);
      }

      // Rate limiting check
      if (!authRateLimiter.isAllowed("register")) {
        throw new Error(
          "Too many registration attempts. Please try again later."
        );
      }

      const res = await secureAuthAPI.register(name, email, password);

      // Update auth state with user data (no token stored in localStorage)
      setAuthState({
        user: res.user,
        isAuthenticated: true,
        loading: false,
        error: null,
      });

      toast({
        title: "Registration successful",
        description: "Welcome to CareerPilotAI!",
      });

      router.push("/dashboard");
    } catch (err: any) {
      console.error("Registration error:", err);
      setAuthState((prev) => ({
        ...prev,
        loading: false,
        error: err.message || "Registration failed",
      }));
      toast({
        title: "Registration failed",
        description: err.message || "Please try again",
        variant: "destructive",
      });
    }
  };

  // Login user with enhanced security
  const login = async (email: string, password: string) => {
    setAuthState((prev) => ({ ...prev, loading: true, error: null }));

    try {
      // Client-side validation
      if (!isValidEmail(email)) {
        throw new Error("Please enter a valid email address");
      }

      if (!password.trim()) {
        throw new Error("Password is required");
      }

      // Rate limiting check
      if (!authRateLimiter.isAllowed("login")) {
        throw new Error("Too many login attempts. Please try again later.");
      }

      const res = await secureAuthAPI.login(email, password);

      // Update auth state with user data (no token stored in localStorage)
      setAuthState({
        user: res.user,
        isAuthenticated: true,
        loading: false,
        error: null,
      });

      toast({
        title: "Login successful",
        description: "Welcome back!",
      });

      router.push("/dashboard");
    } catch (err: any) {
      console.error("Login error:", err);
      setAuthState((prev) => ({
        ...prev,
        loading: false,
        error: err.message || "Login failed",
      }));
      toast({
        title: "Login failed",
        description: err.message || "Invalid credentials",
        variant: "destructive",
      });
    }
  };

  // Logout user with secure cleanup
  const logout = async () => {
    try {
      await secureAuthAPI.logout();
    } catch (err) {
      console.error("Logout error:", err);
    } finally {
      // Clear auth state (httpOnly cookie is cleared by the API)
      setAuthState({
        user: null,
        isAuthenticated: false,
        loading: false,
        error: null,
      });
      toast({
        title: "Logged out",
        description: "You have been logged out successfully",
      });
      router.push("/auth/login");
    }
  };

  return (
    <AuthContext.Provider
      value={{
        ...authState,
        login,
        register,
        logout,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}
