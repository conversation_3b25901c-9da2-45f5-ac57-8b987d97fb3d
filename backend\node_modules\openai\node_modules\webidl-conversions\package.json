{"name": "webidl-conversions", "version": "3.0.1", "description": "Implements the WebIDL algorithms for converting to and from JavaScript values", "main": "lib/index.js", "scripts": {"test": "mocha test/*.js"}, "repository": "jsdom/webidl-conversions", "keywords": ["webidl", "web", "types"], "files": ["lib/"], "author": "Domenic Denicola <<EMAIL>> (https://domenic.me/)", "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"mocha": "^1.21.4"}}