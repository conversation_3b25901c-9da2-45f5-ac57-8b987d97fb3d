"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/status/route";
exports.ids = ["app/api/auth/status/route"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Fstatus%2Froute&page=%2Fapi%2Fauth%2Fstatus%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fstatus%2Froute.ts&appDir=D%3A%5CDev%20Ideas%5CCareerPilotAI-2%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CDev%20Ideas%5CCareerPilotAI-2%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Fstatus%2Froute&page=%2Fapi%2Fauth%2Fstatus%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fstatus%2Froute.ts&appDir=D%3A%5CDev%20Ideas%5CCareerPilotAI-2%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CDev%20Ideas%5CCareerPilotAI-2%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_Dev_Ideas_CareerPilotAI_2_frontend_app_api_auth_status_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/auth/status/route.ts */ \"(rsc)/./app/api/auth/status/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/status/route\",\n        pathname: \"/api/auth/status\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/status/route\"\n    },\n    resolvedPagePath: \"D:\\\\Dev Ideas\\\\CareerPilotAI-2\\\\frontend\\\\app\\\\api\\\\auth\\\\status\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_Dev_Ideas_CareerPilotAI_2_frontend_app_api_auth_status_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/auth/status/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Fstatus%2Froute&page=%2Fapi%2Fauth%2Fstatus%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fstatus%2Froute.ts&appDir=D%3A%5CDev%20Ideas%5CCareerPilotAI-2%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CDev%20Ideas%5CCareerPilotAI-2%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/auth/status/route.ts":
/*!**************************************!*\
  !*** ./app/api/auth/status/route.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   OPTIONS: () => (/* binding */ OPTIONS)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_server_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/server-auth */ \"(rsc)/./lib/server-auth.ts\");\n/**\n * Authentication status check API route\n */ \n\nasync function GET(request) {\n    try {\n        const authResult = await (0,_lib_server_auth__WEBPACK_IMPORTED_MODULE_1__.getAuthenticatedUser)();\n        if (authResult.isAuthenticated) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                user: authResult.user,\n                isAuthenticated: true\n            });\n        } else {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: authResult.error || \"Not authenticated\"\n            }, {\n                status: 401\n            });\n        }\n    } catch (error) {\n        console.error(\"Auth status check error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            message: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n// Handle preflight requests\nasync function OPTIONS() {\n    return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(null, {\n        status: 200,\n        headers: {\n            \"Access-Control-Allow-Origin\": \"http://localhost:3002\" || 0,\n            \"Access-Control-Allow-Methods\": \"GET, OPTIONS\",\n            \"Access-Control-Allow-Headers\": \"Content-Type\",\n            \"Access-Control-Allow-Credentials\": \"true\"\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/auth/status/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/server-auth.ts":
/*!****************************!*\
  !*** ./lib/server-auth.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearAuthCookie: () => (/* binding */ clearAuthCookie),\n/* harmony export */   getAuthCookie: () => (/* binding */ getAuthCookie),\n/* harmony export */   getAuthenticatedUser: () => (/* binding */ getAuthenticatedUser),\n/* harmony export */   setAuthCookie: () => (/* binding */ setAuthCookie),\n/* harmony export */   verifyAuthToken: () => (/* binding */ verifyAuthToken)\n/* harmony export */ });\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/**\n * Server-side authentication utilities using httpOnly cookies\n * This file should only be imported in server components and API routes\n */ \n/**\n * Set authentication cookie (server-side only)\n */ function setAuthCookie(token, maxAge = 30 * 24 * 60 * 60) {\n    const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_0__.cookies)();\n    cookieStore.set(\"token\", token, {\n        httpOnly: true,\n        secure: \"development\" === \"production\",\n        sameSite: \"lax\",\n        maxAge,\n        path: \"/\"\n    });\n}\n/**\n * Get authentication token from cookie (server-side only)\n */ function getAuthCookie() {\n    try {\n        const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_0__.cookies)();\n        const token = cookieStore.get(\"token\");\n        return token?.value || null;\n    } catch (error) {\n        console.error(\"Error getting auth cookie:\", error);\n        return null;\n    }\n}\n/**\n * Clear authentication cookie (server-side only)\n */ function clearAuthCookie() {\n    const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_0__.cookies)();\n    cookieStore.delete(\"token\");\n}\n/**\n * Verify authentication token with backend (server-side only)\n */ async function verifyAuthToken(token) {\n    try {\n        const backendUrl = \"http://localhost:5000\" || 0;\n        const response = await fetch(`${backendUrl}/api/auth/me`, {\n            method: \"GET\",\n            headers: {\n                \"Authorization\": `Bearer ${token}`,\n                \"Content-Type\": \"application/json\",\n                \"Cookie\": `token=${token}`\n            }\n        });\n        if (response.ok) {\n            const userData = await response.json();\n            return {\n                isValid: true,\n                user: userData.data?.user || userData.user\n            };\n        } else {\n            return {\n                isValid: false,\n                error: \"Invalid or expired token\"\n            };\n        }\n    } catch (error) {\n        console.error(\"Token verification error:\", error);\n        return {\n            isValid: false,\n            error: \"Token verification failed\"\n        };\n    }\n}\n/**\n * Get authenticated user from cookie (server-side only)\n */ async function getAuthenticatedUser() {\n    const token = getAuthCookie();\n    if (!token) {\n        return {\n            isAuthenticated: false,\n            error: \"No authentication token found\"\n        };\n    }\n    const verification = await verifyAuthToken(token);\n    if (verification.isValid) {\n        return {\n            isAuthenticated: true,\n            user: verification.user,\n            token: token\n        };\n    } else {\n        // Clear invalid token\n        clearAuthCookie();\n        return {\n            isAuthenticated: false,\n            error: verification.error\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/server-auth.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Fstatus%2Froute&page=%2Fapi%2Fauth%2Fstatus%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fstatus%2Froute.ts&appDir=D%3A%5CDev%20Ideas%5CCareerPilotAI-2%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CDev%20Ideas%5CCareerPilotAI-2%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();