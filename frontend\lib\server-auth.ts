/**
 * Server-side authentication utilities using httpOnly cookies
 * This file should only be imported in server components and API routes
 */

import { cookies } from 'next/headers';

/**
 * Set authentication cookie (server-side only)
 */
export function setAuthCookie(token: string, maxAge: number = 30 * 24 * 60 * 60) {
  const cookieStore = cookies();
  cookieStore.set('token', token, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    maxAge,
    path: '/',
  });
}

/**
 * Get authentication token from cookie (server-side only)
 */
export function getAuthCookie(): string | null {
  try {
    const cookieStore = cookies();
    const token = cookieStore.get('token');
    return token?.value || null;
  } catch (error) {
    console.error('Error getting auth cookie:', error);
    return null;
  }
}

/**
 * Clear authentication cookie (server-side only)
 */
export function clearAuthCookie() {
  const cookieStore = cookies();
  cookieStore.delete('token');
}

/**
 * Verify authentication token with backend (server-side only)
 */
export async function verifyAuthToken(token: string): Promise<{
  isValid: boolean;
  user?: any;
  error?: string;
}> {
  try {
    const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000';
    const response = await fetch(`${backendUrl}/api/auth/me`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
        'Cookie': `token=${token}`,
      },
    });

    if (response.ok) {
      const userData = await response.json();
      return {
        isValid: true,
        user: userData.data?.user || userData.user,
      };
    } else {
      return {
        isValid: false,
        error: 'Invalid or expired token',
      };
    }
  } catch (error) {
    console.error('Token verification error:', error);
    return {
      isValid: false,
      error: 'Token verification failed',
    };
  }
}

/**
 * Get authenticated user from cookie (server-side only)
 */
export async function getAuthenticatedUser(): Promise<{
  isAuthenticated: boolean;
  user?: any;
  token?: string;
  error?: string;
}> {
  const token = getAuthCookie();

  if (!token) {
    return {
      isAuthenticated: false,
      error: 'No authentication token found',
    };
  }

  const verification = await verifyAuthToken(token);

  if (verification.isValid) {
    return {
      isAuthenticated: true,
      user: verification.user,
      token: token,
    };
  } else {
    // Clear invalid token
    clearAuthCookie();
    return {
      isAuthenticated: false,
      error: verification.error,
    };
  }
}
