/**
 * Secure registration API route with httpOnly cookies
 */
import { NextRequest, NextResponse } from 'next/server';
import { setAuthCookie } from '@/lib/server-auth';
import { csrfValidator } from '@/app/api/csrf-token/route';

export async function POST(request: NextRequest) {
  try {
    // CSRF protection
    const csrfToken = request.headers.get('X-CSRF-Token');
    const sessionId = request.cookies.get('session-id')?.value;

    // In development, allow bypass, in production require proper validation
    if (process.env.NODE_ENV === 'production') {
      if (!csrfToken || !sessionId || !csrfValidator(csrfToken, sessionId)) {
        return NextResponse.json(
          { success: false, message: 'Invalid CSRF token' },
          { status: 403 }
        );
      }
    } else {
      // Development mode: just check if token exists
      if (!csrfToken) {
        return NextResponse.json(
          { success: false, message: 'CSRF token required' },
          { status: 403 }
        );
      }
    }

    const body = await request.json();
    const { name, email, password } = body;

    // Input validation
    if (!name || !email || !password) {
      return NextResponse.json(
        { success: false, message: 'Name, email and password are required' },
        { status: 400 }
      );
    }

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { success: false, message: 'Please enter a valid email address' },
        { status: 400 }
      );
    }

    // Password strength validation
    if (password.length < 8) {
      return NextResponse.json(
        { success: false, message: 'Password must be at least 8 characters long' },
        { status: 400 }
      );
    }

    // Forward request to backend API
    const backendResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ name, email, password }),
    });

    const backendData = await backendResponse.json();

    if (backendResponse.ok && backendData.success) {
      // Set httpOnly cookie with the JWT token
      setAuthCookie(backendData.data.token, 30 * 24 * 60 * 60); // 30 days

      // Return user data without the token
      return NextResponse.json({
        success: true,
        message: backendData.message,
        user: backendData.data.user,
      });
    } else {
      return NextResponse.json(
        { 
          success: false, 
          message: backendData.message || 'Registration failed' 
        },
        { status: backendResponse.status }
      );
    }
  } catch (error) {
    console.error('Registration API error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Handle preflight requests
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': process.env.NEXT_PUBLIC_FRONTEND_URL || 'http://localhost:3000',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, X-CSRF-Token',
      'Access-Control-Allow-Credentials': 'true',
    },
  });
}
