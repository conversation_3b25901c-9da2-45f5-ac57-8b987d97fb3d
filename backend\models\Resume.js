const mongoose = require("mongoose");

const ResumeSchema = new mongoose.Schema(
  {
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    title: {
      type: String,
      required: [true, "Please add a title"],
      trim: true,
      maxlength: [100, "Title cannot be more than 100 characters"],
    },
    promptData: {
      jobTitle: {
        type: String,
        required: [true, "Please add a job title"],
      },
      skills: [String],
      experience: {
        type: String,
        required: [true, "Please add experience details"],
      },
      education: {
        type: String,
        required: [true, "Please add education details"],
      },
      additionalInfo: String,
    },
    resultText: {
      type: String,
      required: [true, "Resume content is required"],
    },
    createdAt: {
      type: Date,
      default: Date.now,
    },
    updatedAt: {
      type: Date,
      default: Date.now,
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  }
);

// Indexes for performance optimization
ResumeSchema.index({ user: 1, createdAt: -1 }); // User's resumes sorted by creation date
ResumeSchema.index({ user: 1, title: 1 }); // User's resumes by title
ResumeSchema.index({ createdAt: -1 }); // All resumes by creation date
ResumeSchema.index({ "promptData.jobTitle": 1 }); // Search by job title
ResumeSchema.index({ user: 1, updatedAt: -1 }); // User's resumes by last update

// Text index for search functionality
ResumeSchema.index(
  {
    title: "text",
    "promptData.jobTitle": "text",
    "promptData.skills": "text",
    resultText: "text",
  },
  {
    weights: {
      title: 10,
      "promptData.jobTitle": 8,
      "promptData.skills": 5,
      resultText: 1,
    },
    name: "resume_text_index",
  }
);

// Add virtual field for id
ResumeSchema.virtual("id").get(function () {
  return this._id.toHexString();
});

// Update the updatedAt field before saving
ResumeSchema.pre("save", function (next) {
  this.updatedAt = Date.now();
  next();
});

// Index already created above in the performance optimization section

module.exports = mongoose.model("Resume", ResumeSchema);
