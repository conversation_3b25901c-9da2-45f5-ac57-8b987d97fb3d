const User = require("../models/User");
const responseFormatter = require("../utils/responseFormatter");
const logger = require("../utils/logger");

/**
 * @desc    Register a new user
 * @route   POST /api/auth/register
 * @access  Public
 */
exports.register = async (req, res, next) => {
  try {
    const { name, email, password } = req.body;

    logger.auth("REGISTRATION_ATTEMPT", null, { email });

    // Check if user already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      logger.auth("REGISTRATION_FAILED_USER_EXISTS", null, { email });
      return responseFormatter.error(res, "Email already registered", 400);
    }

    // Create user
    const user = await User.create({
      name,
      email,
      password,
    });

    logger.auth("USER_CREATED", user._id, { email });

    // Generate token
    sendTokenResponse(user, 201, res, "User registered successfully");
  } catch (err) {
    logger.error("Registration error", err);
    next(err);
  }
};

/**
 * @desc    Login user
 * @route   POST /api/auth/login
 * @access  Public
 */
exports.login = async (req, res, next) => {
  try {
    logger.auth("LOGIN_ATTEMPT", null, { email: req.body?.email });

    // Validate request body
    if (!req.body || !req.body.email || !req.body.password) {
      logger.auth("LOGIN_FAILED_MISSING_FIELDS");
      return responseFormatter.error(
        res,
        "Email and password are required",
        400
      );
    }

    const { email, password } = req.body;

    // Check if user exists and is active
    const user = await User.findByEmailSecure(email);

    if (!user) {
      logger.auth("LOGIN_FAILED_USER_NOT_FOUND", null, { email });
      return responseFormatter.error(res, "Invalid credentials", 401);
    }

    // Check if account is locked
    if (user.isLocked) {
      logger.auth("LOGIN_FAILED_ACCOUNT_LOCKED", user._id, {
        email,
        lockUntil: user.lockUntil,
        remainingAttempts: user.remainingAttempts,
      });
      return responseFormatter.error(
        res,
        "Account temporarily locked due to too many failed login attempts. Please try again later.",
        423
      );
    }

    // Check if password matches
    const isMatch = await user.matchPassword(password);
    if (!isMatch) {
      // Increment login attempts
      await user.incLoginAttempts();

      logger.auth("LOGIN_FAILED_INVALID_PASSWORD", user._id, {
        email,
        attempts: user.loginAttempts + 1,
        remainingAttempts: Math.max(0, 4 - user.loginAttempts),
      });

      const remainingAttempts = Math.max(0, 4 - user.loginAttempts);
      const message =
        remainingAttempts > 0
          ? `Invalid credentials. ${remainingAttempts} attempts remaining.`
          : "Invalid credentials. Account will be locked after next failed attempt.";

      return responseFormatter.error(res, message, 401);
    }

    // Reset login attempts on successful login
    if (user.loginAttempts > 0) {
      await user.resetLoginAttempts();
    }

    logger.auth("LOGIN_SUCCESS", user._id, { email });

    // Generate token
    sendTokenResponse(user, 200, res, "Login successful");
  } catch (err) {
    logger.error("Login error", err);
    next(err);
  }
};

/**
 * @desc    Get current logged in user
 * @route   GET /api/auth/me
 * @access  Private
 */
exports.getMe = async (req, res, next) => {
  try {
    const user = await User.findById(req.user.id);

    logger.auth("USER_DATA_RETRIEVED", user._id);

    responseFormatter.success(res, "User data retrieved", {
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        planType: user.planType,
        usageStats: user.usageStats,
        createdAt: user.createdAt,
      },
    });
  } catch (err) {
    logger.error("Get user data error", err);
    next(err);
  }
};

/**
 * @desc    Log user out / clear cookie
 * @route   GET /api/auth/logout
 * @access  Private
 */
exports.logout = (req, res, next) => {
  try {
    // Clear the cookie
    res.cookie("token", "none", {
      expires: new Date(Date.now() + 10 * 1000),
      httpOnly: true,
    });

    logger.auth("USER_LOGOUT", req.user?.id);
    responseFormatter.success(res, "Logged out successfully");
  } catch (err) {
    logger.error("Logout error", err);
    next(err);
  }
};

/**
 * Get token from model, create cookie and send response
 */
const sendTokenResponse = (user, statusCode, res, message) => {
  // Create token
  const token = user.getSignedJwtToken();

  const options = {
    expires: new Date(
      Date.now() + process.env.JWT_COOKIE_EXPIRE * 24 * 60 * 60 * 1000
    ),
    httpOnly: true,
    secure: process.env.NODE_ENV === "production",
    sameSite: "lax",
  };

  // Set cookie and send response
  res.cookie("token", token, options);

  responseFormatter.success(
    res,
    message,
    {
      token,
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        planType: user.planType,
      },
    },
    statusCode
  );
};
